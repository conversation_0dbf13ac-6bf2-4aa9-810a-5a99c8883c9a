#!/usr/bin/env python3
"""
Script: meraki_basic_site_test.py

Basic test to collect essential data for one site using Meraki API.
This will test the connection and collect core data first.
"""

import sys
import json
from typing import Dict, List, Any, Optional
import meraki
from pathlib import Path
from datetime import datetime

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def safe_api_call(func, *args, **kwargs) -> Optional[Any]:
    """Safely make API calls and handle errors"""
    try:
        print(f"    Calling: {func.__name__}")
        result = func(*args, **kwargs)
        print(f"    Success: {func.__name__} - returned {len(result) if isinstance(result, list) else 'data'}")
        return result
    except meraki.APIError as e:
        print(f"    API Error in {func.__name__}: {e}")
        return None
    except Exception as e:
        print(f"    Unexpected Error in {func.__name__}: {e}")
        return None

def get_basic_site_data(dashboard: meraki.DashboardAPI) -> Dict[str, Any]:
    """Get basic data for first site"""
    site_data = {}
    
    try:
        print("Step 1: Getting organizations...")
        orgs = safe_api_call(dashboard.organizations.getOrganizations)
        if not orgs:
            print("No organizations found")
            return site_data
        
        org = orgs[0]
        org_id = org['id']
        site_data['organization'] = org
        print(f"Using organization: {org['name']}")
        
        print("Step 2: Getting networks...")
        networks = safe_api_call(dashboard.organizations.getOrganizationNetworks, org_id)
        if not networks:
            print("No networks found")
            return site_data
        
        # Use first network
        target_network = networks[0]
        network_id = target_network['id']
        site_data['target_network'] = target_network
        print(f"Analyzing site: {target_network['name']}")
        
        print("Step 3: Getting devices...")
        devices = safe_api_call(dashboard.networks.getNetworkDevices, network_id)
        site_data['devices'] = devices
        
        print("Step 4: Getting VLANs...")
        vlans = safe_api_call(dashboard.appliance.getNetworkApplianceVlans, network_id)
        site_data['vlans'] = vlans
        
        print("Step 5: Getting clients...")
        clients = safe_api_call(dashboard.networks.getNetworkClients, network_id, timespan=86400)
        site_data['clients'] = clients
        
        print("Step 6: Getting SSIDs...")
        ssids = safe_api_call(dashboard.wireless.getNetworkWirelessSsids, network_id)
        site_data['ssids'] = ssids
        
        print("Step 7: Getting network details...")
        network_details = safe_api_call(dashboard.networks.getNetwork, network_id)
        site_data['network_details'] = network_details
        
        # Get device details for each device
        if devices:
            print("Step 8: Getting device details...")
            site_data['device_details'] = {}
            for device in devices:
                serial = device['serial']
                device_name = device.get('name', serial)
                print(f"  Getting details for: {device_name}")
                
                device_info = safe_api_call(dashboard.devices.getDevice, serial)
                site_data['device_details'][serial] = device_info
        
        print("Step 9: Getting firewall rules...")
        l3_rules = safe_api_call(dashboard.appliance.getNetworkApplianceFirewallL3FirewallRules, network_id)
        site_data['firewall_l3_rules'] = l3_rules
        
        print("Step 10: Getting site-to-site VPN...")
        vpn_settings = safe_api_call(dashboard.appliance.getNetworkApplianceVpnSiteToSiteVpn, network_id)
        site_data['site_to_site_vpn'] = vpn_settings
        
        print("Basic data collection complete!")
        
    except Exception as e:
        print(f"Error collecting site data: {e}")
    
    return site_data

def save_basic_data(site_data: Dict[str, Any]) -> tuple:
    """Save basic site data to files"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw JSON data
    json_file = Path(f'meraki_basic_site_data_{timestamp}.json')
    with json_file.open('w', encoding='utf-8') as f:
        json.dump(site_data, f, indent=2, default=str)
    
    # Create summary report
    summary_file = Path(f'meraki_basic_site_summary_{timestamp}.txt')
    with summary_file.open('w', encoding='utf-8') as f:
        f.write("MERAKI BASIC SITE DATA COLLECTION\n")
        f.write("=" * 40 + "\n\n")
        
        if 'target_network' in site_data:
            net = site_data['target_network']
            f.write(f"Site Name: {net.get('name', 'N/A')}\n")
            f.write(f"Network ID: {net.get('id', 'N/A')}\n")
            f.write(f"Organization: {site_data.get('organization', {}).get('name', 'N/A')}\n")
            f.write(f"Product Types: {', '.join(net.get('productTypes', []))}\n\n")
        
        # Summary of collected data
        f.write("DATA COLLECTED:\n")
        f.write("-" * 20 + "\n")
        
        devices = site_data.get('devices', [])
        f.write(f"Devices: {len(devices)}\n")
        
        vlans = site_data.get('vlans', [])
        f.write(f"VLANs: {len(vlans)}\n")
        
        clients = site_data.get('clients', [])
        f.write(f"Clients: {len(clients)}\n")
        
        ssids = site_data.get('ssids', [])
        f.write(f"SSIDs: {len(ssids)}\n")
        
        if devices:
            f.write("\nDEVICE SUMMARY:\n")
            f.write("-" * 15 + "\n")
            for device in devices:
                f.write(f"  {device.get('name', 'Unnamed')}: {device.get('model', 'Unknown')} ({device.get('serial', 'N/A')})\n")
        
        if vlans:
            f.write("\nVLAN SUMMARY:\n")
            f.write("-" * 13 + "\n")
            for vlan in vlans:
                f.write(f"  VLAN {vlan.get('id', 'N/A')}: {vlan.get('name', 'Unnamed')} - {vlan.get('subnet', 'N/A')}\n")
        
        f.write(f"\nFull data saved to: {json_file}\n")
    
    return json_file, summary_file

def display_summary(site_data: Dict[str, Any]):
    """Display a quick summary of collected data"""
    print("\n" + "="*50)
    print("COLLECTION SUMMARY")
    print("="*50)
    
    if 'target_network' in site_data:
        print(f"Site: {site_data['target_network']['name']}")
    
    sections = [
        ('Devices', 'devices'),
        ('VLANs', 'vlans'),
        ('Clients', 'clients'),
        ('SSIDs', 'ssids'),
        ('Device Details', 'device_details'),
        ('Firewall Rules', 'firewall_l3_rules'),
        ('VPN Settings', 'site_to_site_vpn')
    ]
    
    for name, key in sections:
        data = site_data.get(key)
        if data is not None:
            if isinstance(data, list):
                print(f"{name}: {len(data)} items")
            elif isinstance(data, dict):
                print(f"{name}: {len(data)} entries")
            else:
                print(f"{name}: collected")
        else:
            print(f"{name}: not available")

def main():
    print("MERAKI BASIC SITE DATA COLLECTION")
    print("=" * 40)
    print("This script will collect essential data for the first site found.")
    print("If successful, we can expand to collect more comprehensive data.\n")
    
    print("Connecting to Meraki dashboard...")
    dashboard = setup_meraki()
    
    print("Collecting basic site data...")
    site_data = get_basic_site_data(dashboard)
    
    if not site_data:
        print("No site data collected.")
        return
    
    # Display summary
    display_summary(site_data)
    
    # Save data
    json_file, summary_file = save_basic_data(site_data)
    
    print(f"\n[+] Data saved to: {json_file}")
    print(f"[+] Summary saved to: {summary_file}")
    
    print("\nIf this worked successfully, I can expand the script to collect")
    print("ALL available data from every API endpoint for comprehensive analysis.")

if __name__ == "__main__":
    main()
