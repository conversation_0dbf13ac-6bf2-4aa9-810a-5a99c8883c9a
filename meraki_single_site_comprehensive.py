#!/usr/bin/env python3
"""
Script: meraki_single_site_comprehensive.py

Collects EVERY possible detail for a single Meraki site using the API.
This script will grab all available data from all API endpoints for one site.

Based on existing script patterns and API usage.

Requirements:
    pip install meraki tabulate
"""

import sys
import json
from typing import Dict, List, Any, Optional
import meraki
from pathlib import Path
from datetime import datetime

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def safe_api_call(func, *args, **kwargs) -> Optional[Any]:
    """Safely make API calls and handle errors"""
    try:
        print(f"    Calling: {func.__name__}")
        result = func(*args, **kwargs)
        print(f"    Success: {func.__name__}")
        return result
    except meraki.APIError as e:
        print(f"    API Error in {func.__name__}: {e}")
        return None
    except Exception as e:
        print(f"    Unexpected Error in {func.__name__}: {e}")
        return None

def get_single_site_comprehensive_data(dashboard: meraki.DashboardAPI, site_name: str = None) -> Dict[str, Any]:
    """Get comprehensive data for a single site"""
    site_data = {}

    try:
        print("Getting organizations...")
        orgs = dashboard.organizations.getOrganizations()
        if not orgs:
            print("No organizations found")
            return site_data

        # Use first organization
        org = orgs[0]
        org_id = org['id']
        site_data['organization'] = org
        print(f"Using organization: {org['name']}")

        print("Getting networks...")
        networks = dashboard.organizations.getOrganizationNetworks(org_id)
        if not networks:
            print("No networks found")
            return site_data

        # Select target network (first one if no specific site name provided)
        target_network = networks[0]
        if site_name:
            # Try to find specific site
            for net in networks:
                if site_name.lower() in net['name'].lower():
                    target_network = net
                    break

        network_id = target_network['id']
        site_data['target_network'] = target_network
        print(f"Analyzing site: {target_network['name']}")

        # === ORGANIZATION LEVEL DATA ===
        print("  Getting organization details...")
        site_data['org_details'] = safe_api_call(dashboard.organizations.getOrganization, org_id)
        site_data['org_admins'] = safe_api_call(dashboard.organizations.getOrganizationAdmins, org_id)
        site_data['org_saml_roles'] = safe_api_call(dashboard.organizations.getOrganizationSamlRoles, org_id)
        site_data['org_login_security'] = safe_api_call(dashboard.organizations.getOrganizationLoginSecurity, org_id)
        site_data['org_licenses'] = safe_api_call(dashboard.organizations.getOrganizationLicenses, org_id)
        site_data['org_inventory'] = safe_api_call(dashboard.organizations.getOrganizationInventoryDevices, org_id)

        # === NETWORK LEVEL DATA ===
        print("  Getting network configuration...")
        site_data['network_details'] = safe_api_call(dashboard.networks.getNetwork, network_id)
        site_data['network_settings'] = safe_api_call(dashboard.networks.getNetworkSettings, network_id)
        site_data['network_alerts'] = safe_api_call(dashboard.networks.getNetworkAlertsSettings, network_id)
        site_data['network_snmp'] = safe_api_call(dashboard.networks.getNetworkSnmp, network_id)
        site_data['network_syslog'] = safe_api_call(dashboard.networks.getNetworkSyslogServers, network_id)
        site_data['network_firmware'] = safe_api_call(dashboard.networks.getNetworkFirmwareUpgrades, network_id)
        site_data['network_traffic'] = safe_api_call(dashboard.networks.getNetworkTraffic, network_id)
        site_data['network_events'] = safe_api_call(dashboard.networks.getNetworkEvents, network_id)

        # === DEVICES ===
        print("  Getting devices...")
        devices = safe_api_call(dashboard.networks.getNetworkDevices, network_id)
        site_data['devices'] = devices

        # Get detailed device information
        if devices:
            site_data['device_details'] = {}
            for device in devices:
                serial = device['serial']
                device_name = device.get('name', serial)
                print(f"    Getting details for device: {device_name}")

                device_info = {
                    'basic_info': device,
                    'device_status': safe_api_call(dashboard.devices.getDevice, serial),
                    'management_interface': safe_api_call(dashboard.devices.getDeviceManagementInterface, serial),
                    'loss_and_latency': safe_api_call(dashboard.organizations.getOrganizationDevicesUplinksLossAndLatency, org_id, serials=[serial])
                }

                # Device-specific data based on model type
                model = device.get('model', '')

                # Switch-specific data
                if model.startswith('MS'):
                    print(f"      Getting switch data for {device_name}")
                    device_info['switch_ports'] = safe_api_call(dashboard.switch.getDeviceSwitchPorts, serial)
                    device_info['switch_port_statuses'] = safe_api_call(dashboard.switch.getDeviceSwitchPortsStatuses, serial)

                # Appliance-specific data
                if model.startswith(('MX', 'Z')):
                    print(f"      Getting appliance data for {device_name}")
                    device_info['appliance_uplinks'] = safe_api_call(dashboard.appliance.getDeviceApplianceUplinksSettings, serial)
                    device_info['appliance_performance'] = safe_api_call(dashboard.appliance.getDeviceAppliancePerformance, serial)

                # Wireless-specific data
                if model.startswith(('MR', 'CW')):
                    print(f"      Getting wireless data for {device_name}")
                    device_info['wireless_status'] = safe_api_call(dashboard.wireless.getDeviceWirelessStatus, serial)
                    device_info['wireless_connection_stats'] = safe_api_call(dashboard.wireless.getDeviceWirelessConnectionStats, serial)

                site_data['device_details'][serial] = device_info

        # === CLIENTS ===
        print("  Getting network clients...")
        site_data['clients'] = safe_api_call(dashboard.networks.getNetworkClients, network_id, timespan=86400)

        # === APPLIANCE SETTINGS ===
        print("  Getting appliance settings...")

        # VLANs
        site_data['vlans'] = safe_api_call(dashboard.appliance.getNetworkApplianceVlans, network_id)

        # Firewall rules
        site_data['firewall_l3_rules'] = safe_api_call(dashboard.appliance.getNetworkApplianceFirewallL3FirewallRules, network_id)
        site_data['firewall_l7_rules'] = safe_api_call(dashboard.appliance.getNetworkApplianceFirewallL7FirewallRules, network_id)
        site_data['firewall_cellular_rules'] = safe_api_call(dashboard.appliance.getNetworkApplianceFirewallCellularFirewallRules, network_id)

        # Port forwarding
        site_data['port_forwarding'] = safe_api_call(dashboard.appliance.getNetworkApplianceFirewallPortForwardingRules, network_id)
        site_data['one_to_one_nat'] = safe_api_call(dashboard.appliance.getNetworkApplianceFirewallOneToOneNatRules, network_id)
        site_data['one_to_many_nat'] = safe_api_call(dashboard.appliance.getNetworkApplianceFirewallOneToManyNatRules, network_id)

        # VPN settings
        site_data['site_to_site_vpn'] = safe_api_call(dashboard.appliance.getNetworkApplianceVpnSiteToSiteVpn, network_id)
        site_data['client_vpn'] = safe_api_call(dashboard.appliance.getNetworkApplianceClientVpnSettings, network_id)

        # Security and content filtering
        site_data['content_filtering'] = safe_api_call(dashboard.appliance.getNetworkApplianceContentFiltering, network_id)
        site_data['malware_settings'] = safe_api_call(dashboard.appliance.getNetworkApplianceSecurityMalware, network_id)
        site_data['intrusion_settings'] = safe_api_call(dashboard.appliance.getNetworkApplianceSecurityIntrusion, network_id)

        # Traffic shaping
        site_data['traffic_shaping'] = safe_api_call(dashboard.appliance.getNetworkApplianceTrafficShaping, network_id)
        site_data['traffic_shaping_rules'] = safe_api_call(dashboard.appliance.getNetworkApplianceTrafficShapingRules, network_id)

        # DHCP settings
        site_data['dhcp_subnets'] = safe_api_call(dashboard.appliance.getNetworkApplianceDhcpSubnets, network_id)

        # Connectivity monitoring
        site_data['connectivity_monitoring'] = safe_api_call(dashboard.appliance.getNetworkApplianceConnectivityMonitoringDestinations, network_id)

        # Warm spare
        site_data['warm_spare'] = safe_api_call(dashboard.appliance.getNetworkApplianceWarmSpare, network_id)

        # === WIRELESS SETTINGS ===
        print("  Getting wireless settings...")

        # SSIDs
        site_data['ssids'] = safe_api_call(dashboard.wireless.getNetworkWirelessSsids, network_id)

        # Wireless settings
        site_data['wireless_settings'] = safe_api_call(dashboard.wireless.getNetworkWirelessSettings, network_id)
        site_data['wireless_billing'] = safe_api_call(dashboard.wireless.getNetworkWirelessBilling, network_id)
        site_data['wireless_bluetooth'] = safe_api_call(dashboard.wireless.getNetworkWirelessBluetoothSettings, network_id)

        # RF profiles
        site_data['rf_profiles'] = safe_api_call(dashboard.wireless.getNetworkWirelessRfProfiles, network_id)

        # Air Marshal
        site_data['air_marshal'] = safe_api_call(dashboard.wireless.getNetworkWirelessAirMarshal, network_id)

        # === SWITCH SETTINGS ===
        print("  Getting switch settings...")

        # Switch settings
        site_data['switch_settings'] = safe_api_call(dashboard.switch.getNetworkSwitchSettings, network_id)
        site_data['switch_mtu'] = safe_api_call(dashboard.switch.getNetworkSwitchMtu, network_id)
        site_data['switch_stp'] = safe_api_call(dashboard.switch.getNetworkSwitchStp, network_id)
        site_data['switch_storm_control'] = safe_api_call(dashboard.switch.getNetworkSwitchStormControl, network_id)

        # Switch stacks
        site_data['switch_stacks'] = safe_api_call(dashboard.switch.getNetworkSwitchStacks, network_id)

        # Access control lists
        site_data['switch_acls'] = safe_api_call(dashboard.switch.getNetworkSwitchAccessControlLists, network_id)

        # DHCP server policy
        site_data['switch_dhcp_server_policy'] = safe_api_call(dashboard.switch.getNetworkSwitchDhcpServerPolicy, network_id)

        # === CAMERA SETTINGS (if applicable) ===
        print("  Getting camera settings...")
        site_data['camera_quality_retention'] = safe_api_call(dashboard.camera.getNetworkCameraQualityRetentionProfiles, network_id)
        site_data['camera_wireless_profiles'] = safe_api_call(dashboard.camera.getNetworkCameraWirelessProfiles, network_id)

        # === SENSOR SETTINGS (if applicable) ===
        print("  Getting sensor settings...")
        site_data['sensor_alerts'] = safe_api_call(dashboard.sensor.getNetworkSensorAlertsProfiles, network_id)

        print(f"  Comprehensive data collection complete for: {target_network['name']}")

    except Exception as e:
        print(f"Error collecting site data: {e}")

    return site_data

def save_comprehensive_data(site_data: Dict[str, Any]) -> tuple:
    """Save comprehensive site data to files"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Save raw JSON data
    json_file = Path(f'meraki_single_site_comprehensive_{timestamp}.json')
    with json_file.open('w', encoding='utf-8') as f:
        json.dump(site_data, f, indent=2, default=str)

    # Create summary report
    summary_file = Path(f'meraki_single_site_summary_{timestamp}.txt')
    with summary_file.open('w', encoding='utf-8') as f:
        f.write("MERAKI SINGLE SITE COMPREHENSIVE DATA COLLECTION\n")
        f.write("=" * 60 + "\n\n")

        if 'target_network' in site_data:
            net = site_data['target_network']
            f.write(f"Site Name: {net.get('name', 'N/A')}\n")
            f.write(f"Network ID: {net.get('id', 'N/A')}\n")
            f.write(f"Organization: {site_data.get('organization', {}).get('name', 'N/A')}\n")
            f.write(f"Product Types: {', '.join(net.get('productTypes', []))}\n")
            f.write(f"Time Zone: {net.get('timeZone', 'N/A')}\n")
            f.write(f"Tags: {', '.join(net.get('tags', []))}\n\n")

        # Count data sections
        data_sections = {}
        for key, value in site_data.items():
            if value is not None:
                if isinstance(value, list):
                    data_sections[key] = f"{len(value)} items"
                elif isinstance(value, dict):
                    data_sections[key] = f"{len(value)} keys"
                else:
                    data_sections[key] = "collected"

        f.write("DATA SECTIONS COLLECTED:\n")
        f.write("-" * 30 + "\n")
        for section, count in sorted(data_sections.items()):
            f.write(f"{section}: {count}\n")

        f.write(f"\nTotal sections: {len(data_sections)}\n")
        f.write(f"Full data saved to: {json_file}\n")

    return json_file, summary_file

def main():
    if len(sys.argv) > 1:
        site_name = sys.argv[1]
        print(f"Looking for site containing: {site_name}")
    else:
        site_name = None
        print("No site name provided, using first site found")

    print("\nConnecting to Meraki dashboard...")
    dashboard = setup_meraki()

    print("Collecting comprehensive data for single site...")
    site_data = get_single_site_comprehensive_data(dashboard, site_name)

    if not site_data:
        print("No site data collected.")
        return

    # Save comprehensive data
    json_file, summary_file = save_comprehensive_data(site_data)

    print(f"\n[+] Comprehensive data saved to: {json_file}")
    print(f"[+] Summary saved to: {summary_file}")

    # Display summary
    data_count = len([k for k, v in site_data.items() if v is not None])
    print(f"\nData sections collected: {data_count}")

    if 'target_network' in site_data:
        print(f"Site analyzed: {site_data['target_network']['name']}")

    print("\nThis script collected EVERY available API endpoint for this site.")
    print("Review the JSON file to see all available data, then let me know what specific")
    print("fields you'd like to extract for all sites.")

if __name__ == "__main__":
    main()
