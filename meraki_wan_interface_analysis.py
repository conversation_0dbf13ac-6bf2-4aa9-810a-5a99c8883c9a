#!/usr/bin/env python3
"""
Script: meraki_wan_interface_analysis.py

Extracts WAN interface information for all Meraki sites including:
- WAN interfaces in use (WAN1, WAN2)
- IP addresses for each WAN interface
- Connection type (DHCP/Static)
- Cellular Gateway presence

Uses existing comprehensive Meraki data.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, NamedTuple
from datetime import datetime
import csv

class WANInterface(NamedTuple):
    interface: str  # WAN1, WAN2
    ip_address: str
    connection_type: str  # Static, DHCP, Unknown

class SiteWANInfo(NamedTuple):
    site_name: str
    network_id: str
    wan_interfaces: List[WANInterface]
    has_cellular_gateway: bool
    cellular_model: str
    primary_device_model: str
    switch_models: List[str]
    switch_count: int
    notes: str

def load_existing_data() -> Dict[str, Any]:
    """Load existing Meraki data from JSON file"""
    data_file = Path('dev/meraki_data/meraki_full_dump_20250303_163123.json')

    if not data_file.exists():
        print(f"Data file not found: {data_file}")
        return {}

    try:
        with data_file.open('r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading data: {e}")
        return {}

def determine_connection_type(wan_ip: str, notes: str) -> str:
    """Determine if WAN connection is Static or DHCP based on available info"""
    if not wan_ip:
        return "No IP"

    # Check notes for DHCP/PPPoE indicators
    notes_lower = notes.lower() if notes else ""

    if "pppoe" in notes_lower or "dhcp" in notes_lower:
        return "DHCP/PPPoE"
    elif "static" in notes_lower:
        return "Static"
    else:
        # If we have a WAN IP but no clear indication, assume Static
        return "Static"

def analyze_site_wan_info(network_data: Dict[str, Any]) -> SiteWANInfo:
    """Analyze WAN information for a single site"""
    site_name = network_data['info']['name']
    network_id = network_data['info']['id']
    devices = network_data.get('devices', [])

    wan_interfaces = []
    has_cellular_gateway = False
    cellular_model = ""
    primary_device_model = ""
    switch_models = []
    combined_notes = ""

    # Find appliance devices (MX, Z, MG series)
    appliance_devices = [d for d in devices if d.get('model', '').startswith(('MX', 'Z', 'MG'))]

    # Find switch devices (MS series)
    switch_devices = [d for d in devices if d.get('model', '').startswith('MS')]

    # Extract switch information
    for switch in switch_devices:
        model = switch.get('model', '')
        if model and model not in switch_models:
            switch_models.append(model)

    for device in appliance_devices:
        model = device.get('model', '')
        notes = device.get('notes', '')
        combined_notes += f"{notes} " if notes else ""

        # Check for cellular gateway
        if model.startswith('MG'):
            has_cellular_gateway = True
            cellular_model = model

        # Set primary device model (prefer MX over others)
        if model.startswith('MX') or not primary_device_model:
            primary_device_model = model

        # Extract WAN interface information
        wan1_ip = device.get('wan1Ip')
        wan2_ip = device.get('wan2Ip')

        if wan1_ip:
            connection_type = determine_connection_type(wan1_ip, notes)
            wan_interfaces.append(WANInterface(
                interface="WAN1",
                ip_address=wan1_ip,
                connection_type=connection_type
            ))

        if wan2_ip:
            connection_type = determine_connection_type(wan2_ip, notes)
            wan_interfaces.append(WANInterface(
                interface="WAN2",
                ip_address=wan2_ip,
                connection_type=connection_type
            ))

    # If no WAN IPs found but we have appliance devices, check for DHCP indication
    if not wan_interfaces and appliance_devices:
        notes_lower = combined_notes.lower()
        if "pppoe" in notes_lower or "dhcp" in notes_lower:
            wan_interfaces.append(WANInterface(
                interface="WAN1",
                ip_address="DHCP (No static IP)",
                connection_type="DHCP/PPPoE"
            ))

    return SiteWANInfo(
        site_name=site_name,
        network_id=network_id,
        wan_interfaces=wan_interfaces,
        has_cellular_gateway=has_cellular_gateway,
        cellular_model=cellular_model,
        primary_device_model=primary_device_model,
        switch_models=switch_models,
        switch_count=len(switch_devices),
        notes=combined_notes.strip()
    )

def analyze_all_sites_wan(data: Dict[str, Any]) -> List[SiteWANInfo]:
    """Analyze WAN information for all sites"""
    all_sites_wan = []

    for org_id, org_data in data.items():
        org_name = org_data['info']['name']
        print(f"Analyzing WAN info for organization: {org_name}")

        networks = org_data.get('networks', {})
        for network_id, network_data in networks.items():
            site_name = network_data['info']['name']
            print(f"  Processing site: {site_name}")

            site_wan_info = analyze_site_wan_info(network_data)
            all_sites_wan.append(site_wan_info)

    return all_sites_wan

def create_wan_report(sites_wan: List[SiteWANInfo]) -> str:
    """Create a formatted WAN interface report"""
    report = []
    report.append("MERAKI SITES WAN INTERFACE ANALYSIS")
    report.append("=" * 60)
    report.append("")
    report.append(f"Total sites analyzed: {len(sites_wan)}")
    report.append("")

    # Summary statistics
    sites_with_wan1 = len([s for s in sites_wan if any(w.interface == "WAN1" for w in s.wan_interfaces)])
    sites_with_wan2 = len([s for s in sites_wan if any(w.interface == "WAN2" for w in s.wan_interfaces)])
    sites_with_cellular = len([s for s in sites_wan if s.has_cellular_gateway])
    sites_with_switches = len([s for s in sites_wan if s.switch_count > 0])
    sites_with_static = len([s for s in sites_wan if any("Static" in w.connection_type for w in s.wan_interfaces)])
    sites_with_dhcp = len([s for s in sites_wan if any("DHCP" in w.connection_type for w in s.wan_interfaces)])
    total_switches = sum(s.switch_count for s in sites_wan)

    report.append("SUMMARY STATISTICS:")
    report.append("-" * 20)
    report.append(f"Sites with WAN1 interface: {sites_with_wan1}")
    report.append(f"Sites with WAN2 interface: {sites_with_wan2}")
    report.append(f"Sites with Cellular Gateway: {sites_with_cellular}")
    report.append(f"Sites with Switches: {sites_with_switches}")
    report.append(f"Total Switches across all sites: {total_switches}")
    report.append(f"Sites with Static IP: {sites_with_static}")
    report.append(f"Sites with DHCP/PPPoE: {sites_with_dhcp}")
    report.append("")

    # Detailed site information
    report.append("DETAILED SITE WAN INFORMATION:")
    report.append("-" * 35)
    report.append("")

    for site in sites_wan:
        report.append(f"Site: {site.site_name}")
        report.append(f"Primary Device: {site.primary_device_model}")

        if site.has_cellular_gateway:
            report.append(f"Cellular Gateway: YES ({site.cellular_model})")
        else:
            report.append("Cellular Gateway: NO")

        # Switch information
        if site.switch_count > 0:
            switch_info = f"Switches: {site.switch_count} ({', '.join(site.switch_models)})"
            report.append(switch_info)
        else:
            report.append("Switches: None")

        if site.wan_interfaces:
            report.append("WAN Interfaces:")
            for wan in site.wan_interfaces:
                report.append(f"  {wan.interface}: {wan.ip_address} ({wan.connection_type})")
        else:
            report.append("WAN Interfaces: None configured")

        # Extract ISP info from notes if available
        if site.notes:
            notes_lines = site.notes.replace('\n', ' | ').strip()
            if len(notes_lines) > 100:
                notes_lines = notes_lines[:100] + "..."
            report.append(f"Notes: {notes_lines}")

        report.append("")

    return "\n".join(report)

def save_wan_data_csv(sites_wan: List[SiteWANInfo]) -> Path:
    """Save WAN data to CSV file"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_file = Path(f'meraki_wan_interfaces_{timestamp}.csv')

    with csv_file.open('w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)

        # Header
        writer.writerow([
            'Site Name',
            'Network ID',
            'Primary Device Model',
            'WAN1 IP',
            'WAN1 Type',
            'WAN2 IP',
            'WAN2 Type',
            'Has Cellular Gateway',
            'Cellular Model',
            'Switch Count',
            'Switch Models',
            'Total WAN Interfaces',
            'ISP Notes'
        ])

        # Data rows
        for site in sites_wan:
            wan1_ip = ""
            wan1_type = ""
            wan2_ip = ""
            wan2_type = ""

            for wan in site.wan_interfaces:
                if wan.interface == "WAN1":
                    wan1_ip = wan.ip_address
                    wan1_type = wan.connection_type
                elif wan.interface == "WAN2":
                    wan2_ip = wan.ip_address
                    wan2_type = wan.connection_type

            # Clean notes for CSV
            clean_notes = site.notes.replace('\n', ' | ').replace('\r', '') if site.notes else ""
            switch_models_str = ', '.join(site.switch_models) if site.switch_models else ""

            writer.writerow([
                site.site_name,
                site.network_id,
                site.primary_device_model,
                wan1_ip,
                wan1_type,
                wan2_ip,
                wan2_type,
                "YES" if site.has_cellular_gateway else "NO",
                site.cellular_model,
                site.switch_count,
                switch_models_str,
                len(site.wan_interfaces),
                clean_notes
            ])

    return csv_file

def main():
    print("MERAKI WAN INTERFACE ANALYSIS")
    print("=" * 35)
    print("Extracting WAN interface information for all sites...")
    print()

    # Load existing data
    data = load_existing_data()
    if not data:
        print("No data available for analysis.")
        return

    # Analyze WAN information for all sites
    print("Analyzing WAN configurations...")
    sites_wan = analyze_all_sites_wan(data)

    if not sites_wan:
        print("No sites found for analysis.")
        return

    # Create report
    print("Generating WAN interface report...")
    report = create_wan_report(sites_wan)

    # Save files
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Save text report
    report_file = Path(f'meraki_wan_analysis_{timestamp}.txt')
    with report_file.open('w', encoding='utf-8') as f:
        f.write(report)

    # Save CSV data
    csv_file = save_wan_data_csv(sites_wan)

    # Display summary
    print("\n" + "="*60)
    print("WAN INTERFACE ANALYSIS COMPLETE")
    print("="*60)
    print(f"Sites analyzed: {len(sites_wan)}")
    print(f"Text report saved to: {report_file}")
    print(f"CSV data saved to: {csv_file}")
    print()
    print("The CSV file contains all WAN interface details for easy analysis.")
    print("Key findings will be displayed in the text report.")

if __name__ == "__main__":
    main()
