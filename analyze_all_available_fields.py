#!/usr/bin/env python3
"""
Script: analyze_all_available_fields.py

Analyzes your existing Meraki JSON data to show ALL available fields and data points.
This will help you decide exactly what fields you want to collect for all sites.

Uses existing comprehensive data to avoid API connectivity issues.
"""

import json
from pathlib import Path
from typing import Dict, Any, Set
from datetime import datetime

def load_existing_data() -> Dict[str, Any]:
    """Load existing Meraki data from JSON file"""
    data_file = Path('dev/meraki_data/meraki_full_dump_20250303_163123.json')
    
    if not data_file.exists():
        print(f"Data file not found: {data_file}")
        return {}
    
    try:
        with data_file.open('r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading data: {e}")
        return {}

def extract_all_field_paths(obj: Any, prefix: str = "", max_depth: int = 10) -> Set[str]:
    """Recursively extract all field paths from a nested object"""
    if max_depth <= 0:
        return set()
    
    paths = set()
    
    if isinstance(obj, dict):
        for key, value in obj.items():
            current_path = f"{prefix}.{key}" if prefix else key
            paths.add(current_path)
            
            # Recurse into nested objects
            if isinstance(value, (dict, list)) and max_depth > 1:
                nested_paths = extract_all_field_paths(value, current_path, max_depth - 1)
                paths.update(nested_paths)
    
    elif isinstance(obj, list) and obj:
        # For lists, analyze the first item to understand structure
        current_path = f"{prefix}[0]" if prefix else "[0]"
        if obj:
            nested_paths = extract_all_field_paths(obj[0], current_path, max_depth - 1)
            paths.update(nested_paths)
    
    return paths

def analyze_data_structure(data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze the complete data structure"""
    analysis = {
        'organizations': {},
        'networks': {},
        'devices': {},
        'vlans': {},
        'clients': {},
        'ssids': {},
        'all_field_paths': set()
    }
    
    for org_id, org_data in data.items():
        print(f"Analyzing organization: {org_data['info']['name']}")
        
        # Organization level fields
        org_fields = extract_all_field_paths(org_data['info'], "org")
        analysis['all_field_paths'].update(org_fields)
        analysis['organizations'][org_id] = {
            'name': org_data['info']['name'],
            'fields': org_fields
        }
        
        # Networks
        networks = org_data.get('networks', {})
        for net_id, net_data in networks.items():
            net_name = net_data['info']['name']
            print(f"  Analyzing network: {net_name}")
            
            # Network level fields
            net_fields = extract_all_field_paths(net_data['info'], "network")
            analysis['all_field_paths'].update(net_fields)
            
            # Device fields
            devices = net_data.get('devices', [])
            if devices:
                device_fields = extract_all_field_paths(devices[0], "device")
                analysis['all_field_paths'].update(device_fields)
                analysis['devices'][net_id] = {
                    'network_name': net_name,
                    'device_count': len(devices),
                    'sample_device_fields': device_fields
                }
            
            # VLAN fields
            vlans = net_data.get('vlans', [])
            if vlans:
                vlan_fields = extract_all_field_paths(vlans[0], "vlan")
                analysis['all_field_paths'].update(vlan_fields)
                analysis['vlans'][net_id] = {
                    'network_name': net_name,
                    'vlan_count': len(vlans),
                    'sample_vlan_fields': vlan_fields
                }
            
            # Client fields
            clients = net_data.get('clients', [])
            if clients:
                client_fields = extract_all_field_paths(clients[0], "client")
                analysis['all_field_paths'].update(client_fields)
                analysis['clients'][net_id] = {
                    'network_name': net_name,
                    'client_count': len(clients),
                    'sample_client_fields': client_fields
                }
            
            # SSID fields
            ssids = net_data.get('ssids', [])
            if ssids:
                ssid_fields = extract_all_field_paths(ssids[0], "ssid")
                analysis['all_field_paths'].update(ssid_fields)
                analysis['ssids'][net_id] = {
                    'network_name': net_name,
                    'ssid_count': len(ssids),
                    'sample_ssid_fields': ssid_fields
                }
    
    return analysis

def create_field_catalog(analysis: Dict[str, Any]) -> str:
    """Create a comprehensive catalog of all available fields"""
    catalog = []
    catalog.append("COMPREHENSIVE MERAKI DATA FIELD CATALOG")
    catalog.append("=" * 60)
    catalog.append("")
    catalog.append("This catalog shows ALL available data fields from your Meraki environment.")
    catalog.append("Use this to decide exactly what fields you want to collect for all sites.")
    catalog.append("")
    
    # Organize fields by category
    field_categories = {
        'Organization Fields': [],
        'Network Fields': [],
        'Device Fields': [],
        'VLAN Fields': [],
        'Client Fields': [],
        'SSID Fields': [],
        'Other Fields': []
    }
    
    for field_path in sorted(analysis['all_field_paths']):
        if field_path.startswith('org'):
            field_categories['Organization Fields'].append(field_path)
        elif field_path.startswith('network'):
            field_categories['Network Fields'].append(field_path)
        elif field_path.startswith('device'):
            field_categories['Device Fields'].append(field_path)
        elif field_path.startswith('vlan'):
            field_categories['VLAN Fields'].append(field_path)
        elif field_path.startswith('client'):
            field_categories['Client Fields'].append(field_path)
        elif field_path.startswith('ssid'):
            field_categories['SSID Fields'].append(field_path)
        else:
            field_categories['Other Fields'].append(field_path)
    
    for category, fields in field_categories.items():
        if fields:
            catalog.append(f"{category.upper()}:")
            catalog.append("-" * len(category))
            for field in fields:
                catalog.append(f"  {field}")
            catalog.append("")
    
    # Summary statistics
    catalog.append("SUMMARY STATISTICS:")
    catalog.append("-" * 20)
    catalog.append(f"Total unique field paths: {len(analysis['all_field_paths'])}")
    catalog.append(f"Organizations analyzed: {len(analysis['organizations'])}")
    catalog.append(f"Networks with devices: {len(analysis['devices'])}")
    catalog.append(f"Networks with VLANs: {len(analysis['vlans'])}")
    catalog.append(f"Networks with clients: {len(analysis['clients'])}")
    catalog.append(f"Networks with SSIDs: {len(analysis['ssids'])}")
    catalog.append("")
    
    # Sample data examples
    catalog.append("SAMPLE DATA EXAMPLES:")
    catalog.append("-" * 20)
    catalog.append("Here are some example values for key fields:")
    catalog.append("")
    
    # Get first organization for examples
    if analysis['organizations']:
        first_org = list(analysis['organizations'].values())[0]
        catalog.append(f"Organization Name: {first_org['name']}")
    
    # Get first network for examples
    if analysis['devices']:
        first_device_info = list(analysis['devices'].values())[0]
        catalog.append(f"Sample Network: {first_device_info['network_name']}")
        catalog.append(f"Device Count: {first_device_info['device_count']}")
    
    if analysis['vlans']:
        first_vlan_info = list(analysis['vlans'].values())[0]
        catalog.append(f"VLAN Count: {first_vlan_info['vlan_count']}")
    
    catalog.append("")
    catalog.append("RECOMMENDED FIELDS FOR SITE INVENTORY:")
    catalog.append("-" * 40)
    catalog.append("Based on your existing scripts, you might want:")
    catalog.append("  • network.name (Site Name)")
    catalog.append("  • device.address (Site Address)")
    catalog.append("  • device.notes (Contact info, ISP details)")
    catalog.append("  • device.tags (Site tags)")
    catalog.append("  • device.model (Device model)")
    catalog.append("  • device.serial (Serial number)")
    catalog.append("  • device.wan1Ip (WAN IP address)")
    catalog.append("  • device.wan2Ip (Secondary WAN IP)")
    catalog.append("  • vlan.subnet (VLAN subnets)")
    catalog.append("  • vlan.name (VLAN names)")
    catalog.append("")
    catalog.append("Let me know which specific fields you want to collect!")
    
    return "\n".join(catalog)

def save_field_analysis(analysis: Dict[str, Any], catalog: str) -> tuple:
    """Save field analysis to files"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save field catalog
    catalog_file = Path(f'meraki_field_catalog_{timestamp}.txt')
    with catalog_file.open('w', encoding='utf-8') as f:
        f.write(catalog)
    
    # Save detailed analysis as JSON
    analysis_file = Path(f'meraki_field_analysis_{timestamp}.json')
    # Convert sets to lists for JSON serialization
    json_analysis = {}
    for key, value in analysis.items():
        if key == 'all_field_paths':
            json_analysis[key] = sorted(list(value))
        else:
            json_analysis[key] = value
            # Convert any sets in nested structures
            if isinstance(value, dict):
                for subkey, subvalue in value.items():
                    if isinstance(subvalue, dict):
                        for subsubkey, subsubvalue in subvalue.items():
                            if isinstance(subsubvalue, set):
                                json_analysis[key][subkey][subsubkey] = sorted(list(subsubvalue))
    
    with analysis_file.open('w', encoding='utf-8') as f:
        json.dump(json_analysis, f, indent=2, default=str)
    
    return catalog_file, analysis_file

def main():
    print("MERAKI DATA FIELD ANALYSIS")
    print("=" * 30)
    print("Analyzing your existing Meraki data to show ALL available fields...")
    print()
    
    # Load existing data
    data = load_existing_data()
    if not data:
        print("No data available for analysis.")
        return
    
    # Analyze data structure
    print("Analyzing data structure...")
    analysis = analyze_data_structure(data)
    
    # Create field catalog
    print("Creating field catalog...")
    catalog = create_field_catalog(analysis)
    
    # Save analysis
    catalog_file, analysis_file = save_field_analysis(analysis, catalog)
    
    # Display summary
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE")
    print("="*60)
    print(f"Total unique field paths found: {len(analysis['all_field_paths'])}")
    print(f"Field catalog saved to: {catalog_file}")
    print(f"Detailed analysis saved to: {analysis_file}")
    print()
    print("The field catalog shows EVERY available data point in your Meraki environment.")
    print("Review the catalog and tell me exactly which fields you want to collect for all sites.")
    print()
    print("For example, you might want:")
    print("  - Site name, address, and contact info")
    print("  - Device models and serial numbers")
    print("  - WAN IP configurations")
    print("  - VLAN information")
    print("  - Specific tags or notes")

if __name__ == "__main__":
    main()
