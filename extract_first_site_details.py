#!/usr/bin/env python3
"""
Script: extract_first_site_details.py

Extracts comprehensive details for the first site from existing Meraki data.
Uses the existing JSON dump to show all available data for one site.

Requirements:
    None - uses existing data files
"""

import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

def load_existing_data() -> Dict[str, Any]:
    """Load existing Meraki data from JSON file"""
    data_file = Path('dev/meraki_data/meraki_full_dump_20250303_163123.json')
    
    if not data_file.exists():
        print(f"Data file not found: {data_file}")
        return {}
    
    try:
        with data_file.open('r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading data: {e}")
        return {}

def extract_first_site_details(data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract comprehensive details for the first site"""
    if not data:
        return {}
    
    # Get first organization
    org_id = list(data.keys())[0]
    org_data = data[org_id]
    
    print(f"Organization: {org_data['info']['name']}")
    print(f"Organization ID: {org_id}")
    
    # Get first network (site)
    networks = org_data.get('networks', {})
    if not networks:
        print("No networks found")
        return {}
    
    network_id = list(networks.keys())[0]
    network_data = networks[network_id]
    
    site_name = network_data['info']['name']
    print(f"First Site: {site_name}")
    print(f"Network ID: {network_id}")
    
    # Extract all available details
    site_details = {
        'organization_info': org_data['info'],
        'network_info': network_data['info'],
        'devices': network_data.get('devices', []),
        'vlans': network_data.get('vlans', []),
        'ssids': network_data.get('ssids', []),
        'clients': network_data.get('clients', []),
        'site_name': site_name,
        'network_id': network_id,
        'organization_id': org_id
    }
    
    return site_details

def analyze_site_details(details: Dict[str, Any]) -> str:
    """Create comprehensive analysis of site details"""
    if not details:
        return "No site details available"
    
    analysis = []
    analysis.append("=" * 80)
    analysis.append("COMPREHENSIVE SITE DETAILS ANALYSIS")
    analysis.append("=" * 80)
    analysis.append("")
    
    # Basic Information
    analysis.append("BASIC SITE INFORMATION:")
    analysis.append("-" * 40)
    analysis.append(f"Site Name: {details['site_name']}")
    analysis.append(f"Network ID: {details['network_id']}")
    analysis.append(f"Organization: {details['organization_info']['name']}")
    analysis.append(f"Organization ID: {details['organization_id']}")
    
    net_info = details['network_info']
    analysis.append(f"Product Types: {', '.join(net_info.get('productTypes', []))}")
    analysis.append(f"Time Zone: {net_info.get('timeZone', 'N/A')}")
    analysis.append(f"Tags: {', '.join(net_info.get('tags', [])) if net_info.get('tags') else 'None'}")
    analysis.append(f"Notes: {net_info.get('notes', 'None')}")
    analysis.append(f"URL: {net_info.get('url', 'N/A')}")
    analysis.append("")
    
    # Organization Details
    org_info = details['organization_info']
    analysis.append("ORGANIZATION DETAILS:")
    analysis.append("-" * 40)
    analysis.append(f"API Enabled: {org_info.get('api', {}).get('enabled', 'Unknown')}")
    analysis.append(f"Licensing Model: {org_info.get('licensing', {}).get('model', 'Unknown')}")
    analysis.append(f"Cloud Region: {org_info.get('cloud', {}).get('region', {}).get('name', 'Unknown')}")
    analysis.append(f"Cloud Host: {org_info.get('cloud', {}).get('region', {}).get('host', {}).get('name', 'Unknown')}")
    
    mgmt_details = org_info.get('management', {}).get('details', [])
    if mgmt_details:
        for detail in mgmt_details:
            analysis.append(f"{detail.get('name', 'Unknown')}: {detail.get('value', 'N/A')}")
    analysis.append("")
    
    # Devices
    devices = details.get('devices', [])
    analysis.append(f"DEVICES ({len(devices)} total):")
    analysis.append("-" * 40)
    
    for i, device in enumerate(devices, 1):
        analysis.append(f"{i}. {device.get('name', 'Unnamed Device')}")
        analysis.append(f"   Model: {device.get('model', 'Unknown')}")
        analysis.append(f"   Serial: {device.get('serial', 'N/A')}")
        analysis.append(f"   MAC: {device.get('mac', 'N/A')}")
        analysis.append(f"   Address: {device.get('address', 'No address')}")
        analysis.append(f"   Firmware: {device.get('firmware', 'Unknown')}")
        
        if device.get('lanIp'):
            analysis.append(f"   LAN IP: {device['lanIp']}")
        if device.get('wan1Ip'):
            analysis.append(f"   WAN1 IP: {device['wan1Ip']}")
        if device.get('wan2Ip'):
            analysis.append(f"   WAN2 IP: {device['wan2Ip']}")
        
        if device.get('lat') and device.get('lng'):
            analysis.append(f"   Location: {device['lat']}, {device['lng']}")
        
        tags = device.get('tags', [])
        if tags:
            analysis.append(f"   Tags: {', '.join(tags)}")
        
        notes = device.get('notes', '')
        if notes:
            # Clean up notes for display
            clean_notes = notes.replace('\n', ' | ')
            analysis.append(f"   Notes: {clean_notes}")
        
        analysis.append("")
    
    # VLANs
    vlans = details.get('vlans', [])
    analysis.append(f"VLANs ({len(vlans)} total):")
    analysis.append("-" * 40)
    
    for vlan in vlans:
        analysis.append(f"VLAN {vlan.get('id', 'N/A')}: {vlan.get('name', 'Unnamed')}")
        analysis.append(f"   Subnet: {vlan.get('subnet', 'N/A')}")
        analysis.append(f"   Appliance IP: {vlan.get('applianceIp', 'N/A')}")
        analysis.append(f"   DHCP Handling: {vlan.get('dhcpHandling', 'Unknown')}")
        analysis.append(f"   DHCP Lease Time: {vlan.get('dhcpLeaseTime', 'Unknown')}")
        analysis.append(f"   DNS Nameservers: {vlan.get('dnsNameservers', 'N/A')}")
        analysis.append(f"   Group Policy ID: {vlan.get('groupPolicyId', 'N/A')}")
        
        # Fixed IP assignments
        fixed_ips = vlan.get('fixedIpAssignments', {})
        if fixed_ips:
            analysis.append("   Fixed IP Assignments:")
            for mac, assignment in fixed_ips.items():
                name = assignment.get('name', 'Unnamed')
                ip = assignment.get('ip', 'N/A')
                analysis.append(f"     {mac}: {ip} ({name})")
        
        # DHCP options
        dhcp_options = vlan.get('dhcpOptions', [])
        if dhcp_options:
            analysis.append("   DHCP Options:")
            for option in dhcp_options:
                analysis.append(f"     Code {option.get('code', 'N/A')}: {option.get('value', 'N/A')}")
        
        analysis.append("")
    
    # SSIDs
    ssids = details.get('ssids', [])
    analysis.append(f"WIRELESS SSIDs ({len(ssids)} total):")
    analysis.append("-" * 40)
    
    if ssids:
        for ssid in ssids:
            analysis.append(f"SSID: {ssid.get('name', 'Unnamed')}")
            analysis.append(f"   Enabled: {ssid.get('enabled', 'Unknown')}")
            analysis.append(f"   VLAN ID: {ssid.get('defaultVlanId', 'N/A')}")
            analysis.append("")
    else:
        analysis.append("No wireless SSIDs configured")
        analysis.append("")
    
    # Clients
    clients = details.get('clients', [])
    analysis.append(f"NETWORK CLIENTS ({len(clients)} total):")
    analysis.append("-" * 40)
    
    # Group clients by status
    online_clients = [c for c in clients if c.get('status') == 'Online']
    offline_clients = [c for c in clients if c.get('status') == 'Offline']
    
    analysis.append(f"Online: {len(online_clients)}")
    analysis.append(f"Offline: {len(offline_clients)}")
    analysis.append("")
    
    # Show first 10 clients as examples
    for i, client in enumerate(clients[:10], 1):
        analysis.append(f"{i}. {client.get('description', 'Unknown Device')}")
        analysis.append(f"   MAC: {client.get('mac', 'N/A')}")
        analysis.append(f"   IP: {client.get('ip', 'N/A')}")
        analysis.append(f"   Status: {client.get('status', 'Unknown')}")
        analysis.append(f"   VLAN: {client.get('vlan', 'N/A')}")
        analysis.append(f"   Manufacturer: {client.get('manufacturer', 'Unknown')}")
        analysis.append(f"   Connection: {client.get('recentDeviceConnection', 'Unknown')}")
        
        if client.get('switchport'):
            analysis.append(f"   Switch Port: {client['switchport']}")
        
        usage = client.get('usage', {})
        if usage:
            total_mb = usage.get('total', 0) / (1024 * 1024)  # Convert to MB
            analysis.append(f"   Data Usage: {total_mb:.2f} MB")
        
        analysis.append("")
    
    if len(clients) > 10:
        analysis.append(f"... and {len(clients) - 10} more clients")
        analysis.append("")
    
    # Summary
    analysis.append("SUMMARY:")
    analysis.append("-" * 40)
    analysis.append(f"Total Devices: {len(devices)}")
    analysis.append(f"Total VLANs: {len(vlans)}")
    analysis.append(f"Total SSIDs: {len(ssids)}")
    analysis.append(f"Total Clients: {len(clients)}")
    analysis.append(f"Online Clients: {len(online_clients)}")
    analysis.append(f"Offline Clients: {len(offline_clients)}")
    
    return "\n".join(analysis)

def save_analysis(analysis: str, details: Dict[str, Any]) -> tuple:
    """Save analysis and raw data to files"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save human-readable analysis
    analysis_file = Path(f'first_site_analysis_{timestamp}.txt')
    with analysis_file.open('w', encoding='utf-8') as f:
        f.write(analysis)
    
    # Save raw JSON data for the site
    json_file = Path(f'first_site_raw_data_{timestamp}.json')
    with json_file.open('w', encoding='utf-8') as f:
        json.dump(details, f, indent=2, default=str)
    
    return analysis_file, json_file

def main():
    print("Loading existing Meraki data...")
    data = load_existing_data()
    
    if not data:
        print("No data available. Please ensure the JSON file exists.")
        return
    
    print("Extracting first site details...")
    site_details = extract_first_site_details(data)
    
    if not site_details:
        print("No site details found.")
        return
    
    print("Analyzing site details...")
    analysis = analyze_site_details(site_details)
    
    # Display analysis
    print("\n" + analysis)
    
    # Save to files
    analysis_file, json_file = save_analysis(analysis, site_details)
    
    print(f"\n[+] Analysis saved to: {analysis_file}")
    print(f"[+] Raw data saved to: {json_file}")
    
    print(f"\nThis analysis shows EVERY available detail for: {site_details['site_name']}")
    print("Review this data and let me know what specific fields you'd like to collect for all sites.")

if __name__ == "__main__":
    main()
