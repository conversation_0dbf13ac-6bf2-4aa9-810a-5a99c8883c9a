{"organization": {"id": "580401401977375879", "name": "MPaaS-Transdev Canada Inc", "url": "https://n839.meraki.com/o/8EqHfaF/manage/organization/overview", "samlConsumerUrl": "https://n839.meraki.com/saml/login/8EqHfaF/KuolMbCvqlMc", "samlConsumerUrls": ["https://n839.meraki.com/saml/login/8EqHfaF/KuolMbCvqlMc", "https://n839.meraki.com/saml/login/8EqHfaF/GcwiQdCvqlMc", "https://n839.meraki.com/saml/login/8EqHfaF/yq27jdCvqlMc"], "api": {"enabled": true}, "licensing": {"model": "per-device"}, "cloud": {"region": {"name": "North America", "host": {"name": "United States"}}}, "management": {"details": [{"name": "customer number", "value": "95235567"}]}}, "network": {"id": "L_3850014731448352773", "organizationId": "580401401977375879", "name": "QC-Quebec - 2680 boul. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "productTypes": ["appliance", "switch"], "timeZone": "America/Montreal", "tags": [], "enrollmentString": null, "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/pwznDapBb/manage/clients", "notes": "", "isBoundToConfigTemplate": false, "isVirtual": false}, "network_details": {"id": "L_3850014731448352773", "organizationId": "580401401977375879", "productTypes": ["appliance", "switch"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/9TMjrapBb/manage/usage/list", "name": "QC-Quebec - 2680 boul. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeZone": "America/Montreal", "enrollmentString": null, "tags": [], "notes": "", "isBoundToConfigTemplate": false, "isVirtual": false}, "devices": [{"lat": 46.80687, "lng": -71.3007, "address": "2680-B boul. <PERSON><PERSON><PERSON><PERSON><PERSON> , Québec , Qc, H1P 2J1", "serial": "Q4AE-XU69-ENGC", "mac": "cc:9c:3e:8d:59:73", "lanIp": "***********", "notes": "Videotron : 78013944-001-7 \nTelus.com.inc\nICCID: 8912230102144952417\n\n<PERSON><PERSON>and\nTél: ************\nemail: <PERSON><PERSON><PERSON>@transdev.com\n#2 sebastien boud<PERSON><PERSON>\nTél:************\nemail : <EMAIL>", "tags": ["Day2", "SP550516", "SS277297"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/9TMjrapBb/manage/nodes/new_list/224971436415347", "networkId": "L_3850014731448352773", "name": "TDCAN-QCQUEB-SW01", "details": [], "model": "MS120-24P", "switchProfileId": null, "firmware": "switch-17-1-3", "floorPlanId": null}, {"lat": 46.8069, "lng": -71.30071, "address": "2680 boul. <PERSON><PERSON><PERSON><PERSON> , Québec , Qc, H1P 2J1", "serial": "Q2HY-23TL-M4HB", "mac": "bc:db:09:bc:d3:f4", "wan1Ip": "***********", "wan2Ip": null, "notes": "Videotron : 78013944-001-7 \nTelus.com.inc\nICCID: 8912230102144952417\n\n<PERSON><PERSON>and\nTél: ************\nemail: <PERSON><PERSON><PERSON>@transdev.com\n#2 sebastien boud<PERSON><PERSON>\nTél:************\nemail : <EMAIL>", "tags": ["Day2", "SP550521", "SS277297"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/pwznDapBb/manage/nodes/new_list/207648947229684", "networkId": "L_3850014731448352773", "name": "TDCAN-QCQUEB-FW01", "details": [], "model": "MX67C-NA", "firmware": "wired-19-1-7", "floorPlanId": null}], "device_details": {}}