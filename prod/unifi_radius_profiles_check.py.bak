#!/usr/bin/env python3
"""
Script: unifi_radius_vlan_check.py

Checks all UniFi sites for RADIUS VLAN assignment settings in both wired and wireless networks.
Reports which sites have RADIUS VLAN support enabled and for which network types.

Requirements:
    pip install requests tabulate
"""

import sys
import getpass
import requests
from typing import List, Dict, Any
from tabulate import tabulate
from urllib3.exceptions import InsecureRequestWarning

# Configuration
UNIFI_HOST = "https://unifi.limocar.int:8443"
USERNAME = "blos01"
VERIFY_SSL = False

# Suppress insecure HTTPS warnings
if not VERIFY_SSL:
    requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def login_unifi(session: requests.Session, password: str) -> bool:
    """Log into the UniFi Controller"""
    login_url = f"{UNIFI_HOST}/api/login"
    payload = {
        "username": USERNAME,
        "password": password
    }
    resp = session.post(login_url, json=payload, verify=VERIFY_SSL)
    return resp.status_code == 200

def get_sites(session: requests.Session) -> List[Dict]:
    """Get all sites from the controller"""
    url = f"{UNIFI_HOST}/api/self/sites"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    return []

def get_radius_settings(session: requests.Session, site: str) -> Dict[str, Any]:
    """Get RADIUS settings for a site"""
    url = f"{UNIFI_HOST}/api/s/{site}/rest/radiusprofile"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    return []

def check_site_radius_settings(session: requests.Session, site_name: str, site_desc: str) -> Dict:
    """Check RADIUS VLAN settings for a site"""
    radius_profiles = get_radius_settings(session, site_name)
    
    result = {
        "site_name": site_name,
        "site_desc": site_desc,
        "wired_enabled": False,
        "wireless_enabled": False,
        "profiles": []
    }
    
    for profile in radius_profiles:
        profile_name = profile.get("name", "Unnamed Profile")
        # Check vlan_enabled for wired networks
        wired_enabled = profile.get("vlan_enabled", False)
        # Check vlan_wlan_mode for wireless networks
        wireless_enabled = profile.get("vlan_wlan_mode") in ["required", "optional"]
        
        if wired_enabled:
            result["wired_enabled"] = True
        if wireless_enabled:
            result["wireless_enabled"] = True
            
        if wired_enabled or wireless_enabled:
            result["profiles"].append({
                "name": profile_name,
                "wired": wired_enabled,
                "wireless": wireless_enabled
            })
    
    return result

def main():
    print(f"UniFi Controller: {UNIFI_HOST}")
    print(f"Logging in as user: {USERNAME}")
    
    # Get password
    password = getpass.getpass("Enter your admin password: ")
    
    # Setup session
    session = requests.Session()
    
    # Login
    if not login_unifi(session, password):
        print("[!] Login failed")
        sys.exit(1)
    
    # Get sites
    sites = get_sites(session)
    if not sites:
        print("[!] No sites found or insufficient permissions")
        sys.exit(0)
    
    print("\nChecking RADIUS VLAN settings across all sites...")
    
    # Collect results
    results = []
    sites_with_radius_vlan = 0
    
    for site in sites:
        site_name = site["name"]
        site_desc = site.get("desc", site_name)
        
        result = check_site_radius_settings(session, site_name, site_desc)
        
        if result["wired_enabled"] or result["wireless_enabled"]:
            sites_with_radius_vlan += 1
            results.append(result)
    
    # Display results
    if results:
        print(f"\nFound {sites_with_radius_vlan} sites with RADIUS VLAN assignment enabled:\n")
        
        for result in results:
            print(f"Site: {result['site_desc']} ({result['site_name']})")
            print("  RADIUS VLAN Assignment:")
            print(f"  - Wired Networks:    {'Enabled' if result['wired_enabled'] else 'Disabled'}")
            print(f"  - Wireless Networks: {'Enabled' if result['wireless_enabled'] else 'Disabled'}")
            
            if result["profiles"]:
                print("\n  RADIUS Profiles:")
                for profile in result["profiles"]:
                    print(f"    • {profile['name']}:")
                    print(f"      - Wired VLAN:    {'Yes' if profile['wired'] else 'No'}")
                    print(f"      - Wireless VLAN: {'Yes' if profile['wireless'] else 'No'}")
            print()
    else:
        print("\nNo sites found with RADIUS VLAN assignment enabled")
    
    print(f"\nTotal sites checked: {len(sites)}")
    print(f"Sites with RADIUS VLAN assignment: {sites_with_radius_vlan}")

if __name__ == "__main__":
    main()
