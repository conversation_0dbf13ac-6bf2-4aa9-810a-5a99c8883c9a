#!/usr/bin/env python3
"""
Script: meraki-to-fortigate-route-sync.py

Lists all Meraki subnets and verifies their existence in FortiGate routing table.
Allows interactive selection of missing routes to configure.
"""

import sys
from typing import List, NamedTuple, Dict, <PERSON><PERSON>
import ipaddress
import meraki
import requests
from tabulate import tabulate
from urllib3.exceptions import InsecureRequestWarning
import json
from pathlib import Path
from datetime import datetime

# Suppress SSL verification warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# Configuration
MERAKI_API_KEY = "****************************************"
FORTIGATE_HOST = "transdevmaster"
FORTIGATE_PORT = 4443
FORTIGATE_API_KEY = "fmtwft00ftNftcsfgwkNrnNpt8mr4g"
FORTIGATE_VERIFY_SSL = False
# FortiGate VPN gateway
FORTIGATE_VPN_GATEWAY = "**********"

# Target subnet ranges we care about
TARGET_RANGES = [
    ipaddress.IPv4Network("*********/16"),
    ipaddress.IPv4Network("**********/16")
]

# Add these constants
MISSING_ROUTES_FILE = "missing_routes_history.json"

class NetworkInfo(NamedTuple):
    org_name: str  # Keep for internal use
    site_name: str
    subnet: str
    vlan_id: str
    vlan_name: str
    route_exists: bool = False
    route_details: str = ""

class MerakiFortiSync:
    def __init__(self):
        self.dashboard = self.setup_meraki()
        self.networks = []
        self.routes = []
        self.previous_missing = self.load_previous_missing()

    @staticmethod
    def setup_meraki() -> meraki.DashboardAPI:
        """Initialize Meraki dashboard API"""
        return meraki.DashboardAPI(
            api_key=MERAKI_API_KEY,
            output_log=False,
            print_console=False,
            suppress_logging=True
        )

    def get_vpn_subnets(self, network_id: str) -> Dict[str, bool]:
        """Get VPN status for each subnet"""
        vpn_status = {}
        try:
            vpn_config = self.dashboard.appliance.getNetworkApplianceVpnSiteToSiteVpn(network_id)
            
            # If VPN is not enabled globally, return empty dict
            if not vpn_config.get('mode') in ['spoke', 'hub']:
                return vpn_status
                
            # Check subnets in VPN config
            for subnet in vpn_config.get('subnets', []):
                local_subnet = subnet.get('localSubnet')
                if local_subnet and subnet.get('useVpn', False):
                    vpn_status[local_subnet] = True
                    
            return vpn_status
        except meraki.APIError:
            return vpn_status

    def get_meraki_vlans(self) -> List[NetworkInfo]:
        """Collect all VPN-enabled VLANs from Meraki networks"""
        networks = []
        
        for org in self.dashboard.organizations.getOrganizations():
            for net in self.dashboard.organizations.getOrganizationNetworks(org['id']):
                # Skip Ontario sites
                if 'ON-' in net['name']:
                    continue
                    
                if 'appliance' not in net.get('productTypes', []):
                    continue
                    
                try:
                    # Get VPN-enabled subnets first
                    vpn_subnets = self.get_vpn_subnets(net['id'])
                    
                    # Then check VLANs
                    vlans = self.dashboard.appliance.getNetworkApplianceVlans(net['id'])
                    for vlan in vlans:
                        if not vlan.get('subnet'):
                            continue
                            
                        try:
                            subnet = ipaddress.ip_network(vlan['subnet'])
                            # Only include if subnet is VPN-enabled
                            if vlan['subnet'] in vpn_subnets:
                                networks.append(NetworkInfo(
                                    org_name=org['name'],
                                    site_name=net['name'],
                                    subnet=str(subnet),
                                    vlan_id=str(vlan.get('id', 'N/A')),
                                    vlan_name=vlan.get('name', 'Default')
                                ))
                        except ValueError:
                            continue
                except meraki.APIError:
                    continue
        
        return networks

    def get_fortigate_routes(self) -> List[Dict]:
        """Fetch routes from FortiGate"""
        session = requests.Session()
        session.verify = FORTIGATE_VERIFY_SSL
        session.headers.update({
            'Authorization': f'Bearer {FORTIGATE_API_KEY}',
            'Content-Type': 'application/json'
        })
        
        try:
            response = session.get(
                f"https://{FORTIGATE_HOST}:{FORTIGATE_PORT}/api/v2/monitor/router/ipv4"
            )
            response.raise_for_status()
            return response.json().get('results', [])
        except requests.exceptions.RequestException as e:
            print(f"Error fetching FortiGate routes: {e}")
            return []

    def check_route_exists(self, subnet: str) -> Tuple[bool, str]:
        """Check if a subnet exists in FortiGate routes with specific gateway"""
        try:
            subnet_network = ipaddress.ip_network(subnet)
            for route in self.routes:
                # Skip routes not using our VPN gateway
                if route['gateway'] != FORTIGATE_VPN_GATEWAY:
                    continue
                    
                route_network = ipaddress.ip_network(route['ip_mask'])
                if subnet_network == route_network:
                    return True, f"Via {route['gateway']} [{route['distance']}/{route['priority']}] on {route['interface']}"
                elif subnet_network.subnet_of(route_network):
                    return True, f"Covered by larger route {route_network} via {route['gateway']} on {route['interface']}"
            return False, "No matching route found"
        except ValueError as e:
            return False, f"Invalid subnet format: {e}"

    def check_meraki_subnet_exists(self, route_network: str) -> Tuple[bool, str]:
        """Check if a FortiGate route exists in Meraki subnets"""
        try:
            route_net = ipaddress.ip_network(route_network)
            for network in self.networks:
                meraki_net = ipaddress.ip_network(network.subnet)
                if route_net == meraki_net:
                    return True, f"Found in {network.site_name} ({network.vlan_name})"
                elif route_net.subnet_of(meraki_net):
                    return True, f"Covered by larger subnet {meraki_net} in {network.site_name}"
            return False, "No matching Meraki subnet found"
        except ValueError as e:
            return False, f"Invalid subnet format: {e}"

    @staticmethod
    def extract_city_and_address(site_name: str) -> Tuple[str, str]:
        """Extract city and address code from site name"""
        provinces = ['ON-', 'QC-', 'BC-', 'AB-', 'SK-', 'MB-', 'NB-', 'NS-', 'PE-', 'NL-', 'YT-', 'NT-', 'NU-']
        
        for province in provinces:
            if province in site_name:
                after_province = site_name.split(province)[1].strip()
                for i, char in enumerate(after_province):
                    if char.isdigit() or char == '-':
                        return after_province[:i].strip(), after_province[i:].strip()
        
        return "", ""

    def load_previous_missing(self) -> set:
        """Load previously recorded missing routes"""
        try:
            if Path(MISSING_ROUTES_FILE).exists():
                with open(MISSING_ROUTES_FILE, 'r') as f:
                    data = json.load(f)
                    return set(data.get('missing_routes', []))
            return set()
        except Exception as e:
            print(f"Warning: Could not load previous missing routes: {e}")
            return set()

    def save_missing_routes(self, missing_networks: List[NetworkInfo]):
        """Save current missing routes with timestamp"""
        try:
            missing_subnets = [n.subnet for n in missing_networks]
            data = {
                'timestamp': datetime.now().isoformat(),
                'missing_routes': missing_subnets
            }
            with open(MISSING_ROUTES_FILE, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save missing routes: {e}")

    def select_missing_routes_for_group(self, missing_networks: List[NetworkInfo], group_name: str) -> List[NetworkInfo]:
        """Interactive selection of routes for a specific address group"""
        if not missing_networks:
            return []

        print(f"\nSelect routes for {group_name}:")
        for i, network in enumerate(missing_networks, 1):
            # Add asterisk for previously missing routes
            persistent = "*" if network.subnet in self.previous_missing else " "
            print(f"{persistent}{i}. {network.subnet} - {network.vlan_name} ({network.site_name})")
        
        print(f"\nEnter the numbers of the routes you want to configure in {group_name} (comma-separated)")
        print("Example: 1,3,5 or 'all' for all routes, or 0 for none or 'q' to quit")
        print("Note: * indicates routes that were missing in the previous run")
        
        while True:
            choice = input("\nYour selection: ").strip().lower()
            
            if choice == 'q':
                return None  # Signal to quit entirely
            
            if choice == '0':
                return []
                
            if choice == 'all':
                return missing_networks
            
            try:
                selections = [int(x.strip()) for x in choice.split(',')]
                if all(1 <= x <= len(missing_networks) for x in selections):
                    return [missing_networks[i-1] for i in selections]
                else:
                    print(f"Please enter numbers between 1 and {len(missing_networks)}")
            except ValueError:
                print("Invalid input. Please enter comma-separated numbers, 'all', 0, or 'q'")

    def select_missing_routes(self, missing_networks: List[NetworkInfo]) -> Dict[str, List[NetworkInfo]]:
        """Interactive selection of routes to configure for each address group"""
        if not missing_networks:
            return {}

        groups = {
            'TELUS-MERAKI-CORPO': [],
            'TELUS-MERAKI-ME': [],
            'TELUS-MERAKI-MGMT': []
        }
        
        remaining_networks = missing_networks.copy()
        
        for group_name in groups.keys():
            selected = self.select_missing_routes_for_group(remaining_networks, group_name)
            if selected is None:  # User quit
                return {}
                
            groups[group_name] = selected
            # Remove selected networks from remaining options
            remaining_networks = [n for n in remaining_networks if n not in selected]
            if not remaining_networks:
                break
                
        return groups

    def generate_fortigate_commands(self, group_networks: Dict[str, List[NetworkInfo]]) -> None:
        """Generate FortiGate CLI commands for selected routes"""
        if not any(group_networks.values()):
            return

        print("\nFortiGate Configuration Commands:")
        print("config firewall address")
        
        # Track which objects go to which groups
        group_members = {group: [] for group in group_networks.keys()}
        
        # Create all address objects first
        for group_name, networks in group_networks.items():
            for network in networks:
                city, address = self.extract_city_and_address(network.site_name)
                if not city or not address:
                    print(f"# Warning: Could not parse city/address from {network.site_name}, skipping...")
                    continue
                    
                address = ''.join(filter(str.isdigit, address))
                obj_name = f"Network {city}{address} {network.vlan_name}"
                
                print(f'    edit "{obj_name}"')
                print(f'        set subnet {network.subnet}')
                print('        set allow-routing enable')
                print('    next')
                
                group_members[group_name].append(obj_name)
        
        print("end")
        
        # Add objects to their respective groups
        if any(members for members in group_members.values()):
            print("\nconfig firewall addrgrp")
            for group_name, members in group_members.items():
                if members:
                    print(f'    edit "{group_name}"')
                    for member in members:
                        print(f'        append member "{member}"')
                    print('    next')
            print("end")

    def run(self):
        """Main execution flow"""
        print("\nGathering Meraki VLAN subnets...")
        self.networks = self.get_meraki_vlans()
        
        print("Fetching FortiGate routes...")
        self.routes = self.get_fortigate_routes()
        
        # Check route existence in FortiGate
        for i, network in enumerate(self.networks):
            route_exists, route_details = self.check_route_exists(network.subnet)
            self.networks[i] = network._replace(
                route_exists=route_exists,
                route_details=route_details
            )
        
        if not self.networks:
            print("\nNo matching networks found.")
            return
        
        # Display Meraki networks results
        headers = ["Site Name", "Subnet", "VLAN ID", "VLAN Name", "Route Status"]
        rows = [
            [n.site_name, n.subnet, n.vlan_id, n.vlan_name,
             "✓ " + n.route_details if n.route_exists else "✗ " + n.route_details]
            for n in self.networks
        ]
        
        print("\nMeraki Networks and FortiGate Routes:")
        print(tabulate(rows, headers=headers, tablefmt='simple'))
        
        # Check FortiGate routes not in Meraki
        orphaned_routes = []
        for route in self.routes:
            # Skip default routes and internal networks
            if route['ip_mask'] in ['0.0.0.0/0', '10.0.0.0/8']:
                continue
                
            exists, details = self.check_meraki_subnet_exists(route['ip_mask'])
            if not exists:
                orphaned_routes.append({
                    'subnet': route['ip_mask'],
                    'gateway': route['gateway'],
                    'interface': route['interface'],
                    'distance': route['distance'],
                    'priority': route['priority']
                })
        
        # Display orphaned routes
        if orphaned_routes:
            print("\nRoutes in FortiGate but not in Meraki:")
            headers = ["Subnet", "Gateway", "Interface", "Distance/Priority"]
            rows = [
                [r['subnet'], r['gateway'], r['interface'], f"{r['distance']}/{r['priority']}"]
                for r in orphaned_routes
            ]
            print(tabulate(rows, headers=headers, tablefmt='simple'))
            print(f"\nOrphaned routes found: {len(orphaned_routes)}")
        
        # Display summary
        missing = [n for n in self.networks if not n.route_exists]
        print(f"\nSummary:")
        print(f"Total Meraki subnets found: {len(self.networks)}")
        print(f"Missing routes in FortiGate: {len(missing)}")
        print(f"Orphaned routes in FortiGate: {len(orphaned_routes)}")
        
        # Get and save missing routes
        self.save_missing_routes(missing)
        
        if missing:
            selected_networks = self.select_missing_routes(missing)
            if selected_networks:
                print(f"\nGenerating configuration for selected routes...")
                self.generate_fortigate_commands(selected_networks)
            else:
                print("\nNo routes selected for configuration.")

def main():
    try:
        sync = MerakiFortiSync()
        sync.run()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
1
