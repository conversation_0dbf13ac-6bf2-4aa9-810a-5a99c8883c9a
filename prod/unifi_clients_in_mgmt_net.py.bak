#!/usr/bin/env python3
"""
Script: unifi_90net_clients.py

Lists all WiFi clients connected to 10.90.x.x networks across all UniFi sites.
Displays site name, client name, and IP address.

Requirements:
    pip install requests tabulate
"""

import sys
import getpass
import requests
import ipaddress
from typing import List, Dict
from tabulate import tabulate

# Configuration
UNIFI_HOST = "https://unifi.limocar.int:8443"
USERNAME = "blos01"
VERIFY_SSL = False
TARGET_NETWORK = ipaddress.ip_network("*********/16")

def login_unifi(session: requests.Session, password: str) -> bool:
    """Log into the UniFi Controller"""
    login_url = f"{UNIFI_HOST}/api/login"
    payload = {
        "username": USERNAME,
        "password": password
    }
    resp = session.post(login_url, json=payload, verify=VERIFY_SSL)
    return resp.status_code == 200

def get_sites(session: requests.Session) -> List[Dict]:
    """Get all sites from the controller"""
    url = UNIFI_HOST + "/api/self/sites"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    # Fallback for older controllers
    fallback_url = UNIFI_HOST + "/api/stat/sites"
    resp = session.get(fallback_url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    return []

def get_clients_for_site(session: requests.Session, site_name: str) -> List[Dict]:
    """Get all active clients for a specific site"""
    url = f"{UNIFI_HOST}/api/s/{site_name}/stat/sta"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    return []

def is_ip_in_target_network(ip: str) -> bool:
    """Check if IP is in the 10.90.x.x range"""
    try:
        return ipaddress.ip_address(ip) in TARGET_NETWORK
    except ValueError:
        return False

def main():
    print(f"UniFi Controller: {UNIFI_HOST}")
    print(f"Logging in as user: {USERNAME}")

    # Get password
    password = getpass.getpass("Enter your admin password: ")

    # Setup session
    session = requests.Session()
    if not VERIFY_SSL:
        requests.packages.urllib3.disable_warnings()

    # Login
    if not login_unifi(session, password):
        print("[!] Login failed")
        sys.exit(1)

    # Get sites
    sites = get_sites(session)
    if not sites:
        print("[!] No sites found or insufficient permissions")
        sys.exit(0)

    # Collect client information across all sites
    client_data = []
    total_clients = 0
    
    for site in sites:
        site_name = site["name"]
        site_desc = site.get("desc", site_name)
        
        clients = get_clients_for_site(session, site_name)
        for client in clients:
            ip = client.get("ip")
            if ip and is_ip_in_target_network(ip):
                client_data.append([
                    site_desc,
                    client.get("name", client.get("hostname", "Unknown")),
                    ip
                ])
                total_clients += 1

    # Display results
    if client_data:
        print("\nClients in 10.90.x.x networks:")
        print(tabulate(
            client_data,
            headers=["Site", "Client Name", "IP Address"],
            tablefmt="simple"
        ))
        print(f"\nTotal Clients: {total_clients}")
    else:
        print("\nNo clients found in 10.90.x.x networks")

if __name__ == "__main__":
    main()
