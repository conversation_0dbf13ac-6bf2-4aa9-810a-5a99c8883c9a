#!/usr/bin/env python3
"""
Script: fortigate-meraki-subnet-audit.py

Compares FortiGate routes with Meraki subnets to identify routes that exist in FortiGate
but not in Meraki networks, specifically for the TELUS-MERAKIS interface.
"""

import sys
from typing import List, NamedTup<PERSON>, Dict, <PERSON>ple
import ipaddress
import meraki
import requests
from tabulate import tabulate
from urllib3.exceptions import InsecureRequestWarning

# Suppress SSL verification warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# Configuration
MERAKI_API_KEY = "****************************************"
FORTIGATE_HOST = "transdevmaster"
FORTIGATE_PORT = 4443
FORTIGATE_API_KEY = "fmtwft00ftNftcsfgwkNrnNpt8mr4g"
FORTIGATE_VERIFY_SSL = False
TARGET_INTERFACE = "TELUS-MERAKIS"

class NetworkInfo(NamedTuple):
    org_name: str
    site_name: str
    subnet: str
    vlan_id: str
    vlan_name: str

class SubnetAuditor:
    def __init__(self):
        self.dashboard = self.setup_meraki()
        self.meraki_networks = []
        self.fortigate_routes = []
        self.fortigate_addresses = {}

    @staticmethod
    def setup_meraki() -> meraki.DashboardAPI:
        """Initialize Meraki dashboard API"""
        return meraki.DashboardAPI(
            api_key=MERAKI_API_KEY,
            output_log=False,
            print_console=False,
            suppress_logging=True
        )

    def get_meraki_subnets(self) -> List[NetworkInfo]:
        """Collect all subnets from Meraki networks"""
        networks = []
        
        for org in self.dashboard.organizations.getOrganizations():
            for net in self.dashboard.organizations.getOrganizationNetworks(org['id']):
                if 'appliance' not in net.get('productTypes', []):
                    continue
                    
                try:
                    vlans = self.dashboard.appliance.getNetworkApplianceVlans(net['id'])
                    for vlan in vlans:
                        if not vlan.get('subnet'):
                            continue
                            
                        networks.append(NetworkInfo(
                            org_name=org['name'],
                            site_name=net['name'],
                            subnet=vlan['subnet'],
                            vlan_id=str(vlan['id']),
                            vlan_name=vlan.get('name', 'Default')
                        ))
                except Exception as e:
                    print(f"Error getting VLANs for network {net['name']}: {e}")
                    continue
        
        return networks

    def get_fortigate_addresses(self) -> Dict:
        """Fetch all firewall addresses from FortiGate"""
        url = f"https://{FORTIGATE_HOST}:{FORTIGATE_PORT}/api/v2/cmdb/firewall/address"
        headers = {"Authorization": f"Bearer {FORTIGATE_API_KEY}"}
        
        try:
            response = requests.get(url, headers=headers, verify=FORTIGATE_VERIFY_SSL)
            response.raise_for_status()
            addresses = response.json().get('results', [])
            
            # Debug print
            print("\nDebug: First few address objects:")
            for addr in addresses[:5]:
                print(f"Name: {addr.get('name')}, Type: {addr.get('type')}, Subnet: {addr.get('subnet')}")
            
            # Create a mapping of subnet to address object name
            subnet_map = {}
            for addr in addresses:
                if addr.get('type') == 'ipmask':
                    subnet = addr.get('subnet')
                    if subnet:
                        # Handle both IP/mask and IP/netmask formats
                        try:
                            if ' ' in subnet:  # Format: "IP netmask"
                                ip, netmask = subnet.split()
                                network = ipaddress.IPv4Network(f"{ip}/{netmask}", strict=False)
                            else:  # Format: "IP/prefix"
                                network = ipaddress.IPv4Network(subnet, strict=False)
                            subnet_map[str(network)] = addr['name']
                        except ValueError as e:
                            print(f"Warning: Could not parse subnet {subnet}: {e}")
            
            # Debug print
            print("\nDebug: First few parsed subnets:")
            for subnet, name in list(subnet_map.items())[:5]:
                print(f"Subnet: {subnet}, Name: {name}")
            
            return subnet_map
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching FortiGate addresses: {e}")
            return {}

    def get_fortigate_routes(self) -> List[Dict]:
        """Fetch all routes from FortiGate"""
        url = f"https://{FORTIGATE_HOST}:{FORTIGATE_PORT}/api/v2/monitor/router/ipv4"
        headers = {"Authorization": f"Bearer {FORTIGATE_API_KEY}"}
        
        try:
            response = requests.get(url, headers=headers, verify=FORTIGATE_VERIFY_SSL)
            response.raise_for_status()
            routes = response.json().get('results', [])
            # Filter routes for TELUS-MERAKIS interface only
            return [r for r in routes if r.get('interface') == TARGET_INTERFACE]
        except requests.exceptions.RequestException as e:
            print(f"Error fetching FortiGate routes: {e}")
            return []

    def check_route_in_meraki(self, route_network: str) -> Tuple[bool, str]:
        """Check if a FortiGate route exists in Meraki subnets"""
        try:
            route_net = ipaddress.ip_network(route_network)
            for network in self.meraki_networks:
                meraki_net = ipaddress.ip_network(network.subnet)
                if route_net == meraki_net:
                    return True, f"Found in {network.site_name} ({network.vlan_name})"
                elif route_net.subnet_of(meraki_net):
                    return True, f"Covered by larger subnet {meraki_net} in {network.site_name}"
            return False, "No matching Meraki subnet found"
        except ValueError as e:
            return False, f"Invalid subnet format: {e}"

    def run(self):
        """Main execution flow"""
        print("\nGathering Meraki subnets...")
        self.meraki_networks = self.get_meraki_subnets()
        
        print(f"\nFetching FortiGate routes for {TARGET_INTERFACE} interface...")
        self.fortigate_routes = self.get_fortigate_routes()
        
        print("\nFetching FortiGate address objects...")
        self.fortigate_addresses = self.get_fortigate_addresses()
        
        print(f"\nFound {len(self.meraki_networks)} subnets in Meraki")
        print(f"Found {len(self.fortigate_routes)} routes in FortiGate on {TARGET_INTERFACE} interface")
        print(f"Found {len(self.fortigate_addresses)} address objects in FortiGate")
        
        # Find routes in FortiGate but not in Meraki
        orphaned_routes = []
        for route in self.fortigate_routes:
            # Skip default routes and internal networks
            if route['ip_mask'] in ['0.0.0.0/0', '10.0.0.0/8']:
                continue
            
            try:
                route_network = ipaddress.IPv4Network(route['ip_mask'], strict=False)
                exists, details = self.check_route_in_meraki(str(route_network))
                if not exists:
                    addr_obj = self.fortigate_addresses.get(str(route_network))
                    
                    orphaned_routes.append({
                        'Subnet': route['ip_mask'],
                        'Gateway': route['gateway'],
                        'Address Object': addr_obj or 'Not Found'
                    })
            except ValueError as e:
                print(f"Warning: Could not parse route subnet {route['ip_mask']}: {e}")

        # Display results in table format
        if orphaned_routes:
            print("\nRoutes found in FortiGate but not in Meraki:")
            print(tabulate(
                orphaned_routes,
                headers='keys',
                tablefmt='simple'
            ))
        else:
            print("\nNo orphaned routes found.")

def main():
    try:
        auditor = SubnetAuditor()
        auditor.run()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()