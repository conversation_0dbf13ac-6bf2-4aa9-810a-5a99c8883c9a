#!/usr/bin/env python3
"""
Script: teams_test.py

Sends a message to Microsoft Teams channel using webhook.
Usage: ./teams_test.py "Your message here"
"""

import requests
import json
import sys

def send_teams_message(webhook_url: str, message: str) -> bool:
    """
    Send a message to Teams channel using webhook
    Returns True if successful, False otherwise
    """
    # Teams message card format
    payload = {
        "text": message
    }

    try:
        response = requests.post(
            webhook_url,
            data=json.dumps(payload),
            headers={'Content-Type': 'application/json'}
        )
        
        # Check if the request was successful
        if response.status_code == 200:
            print("Message sent successfully!")
            return True
        else:
            print(f"Failed to send message. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error sending message: {e}")
        return False

def main():
    # Check if message was provided
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} \"Your message here\"")
        sys.exit(1)

    # Teams webhook URL
    webhook_url = "https://lehub.webhook.office.com/webhookb2/d3d02a8f-aa5c-4a85-9943-eabc71f813a9@b4518aa8-0d3e-4d10-bc77-4cd7dede3446/IncomingWebhook/be4bfc8d27a3455f831882a830fcfd86/707fa8b5-a16b-42f9-be8a-27de566a25ac/V2ZsIqldeqTNSUFSogg7krMTsuncFlHA-b1YFfY6qDJxc1"
    
    # Get message from command line arguments
    message = " ".join(sys.argv[1:])
    
    # Send the message
    send_teams_message(webhook_url, message)

if __name__ == "__main__":
    main()