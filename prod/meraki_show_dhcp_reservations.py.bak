#!/usr/bin/env python3
"""
Script: meraki_dhcp_reservations.py

Lists all DHCP reservations across Meraki networks, showing:
- Site/Network Name
- VLAN ID and Name
- MAC Address
- Reserved IP
- Reservation Name/Description
"""

import sys
from typing import List, Dict
import meraki
from tabulate import tabulate

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

class DHCPReservation:
    def __init__(self, site: str, vlan_id: str, vlan_name: str, mac: str, ip: str, name: str):
        self.site = site
        self.vlan_id = vlan_id
        self.vlan_name = vlan_name
        self.mac = mac
        self.ip = ip
        self.name = name

    def to_row(self) -> List[str]:
        return [
            self.site,
            self.vlan_id,
            self.vlan_name,
            self.mac,
            self.ip,
            self.name
        ]

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def get_dhcp_reservations(dashboard: meraki.DashboardAPI) -> List[DHCPReservation]:
    """Collect all DHCP reservations from all networks"""
    reservations = []
    
    try:
        # Get all organizations
        orgs = dashboard.organizations.getOrganizations()
        
        for org in orgs:
            print(f"Processing organization: {org['name']}")
            
            try:
                # Get all networks in the organization
                networks = dashboard.organizations.getOrganizationNetworks(org['id'])
                
                for net in networks:
                    if 'appliance' in net.get('productTypes', []):
                        print(f"  Processing network: {net['name']}")
                        
                        try:
                            # Get VLANs for the network
                            vlans = dashboard.appliance.getNetworkApplianceVlans(net['id'])
                            
                            for vlan in vlans:
                                try:
                                    # Get VLAN details including DHCP config
                                    vlan_details = dashboard.appliance.getNetworkApplianceVlan(
                                        net['id'],
                                        vlan['id']
                                    )
                                    
                                    # Check if there are any fixed IP assignments
                                    if 'fixedIpAssignments' in vlan_details:
                                        for mac, config in vlan_details['fixedIpAssignments'].items():
                                            reservations.append(DHCPReservation(
                                                site=net['name'],
                                                vlan_id=str(vlan['id']),
                                                vlan_name=vlan.get('name', 'Default'),
                                                mac=mac,
                                                ip=config.get('ip', 'N/A'),
                                                name=config.get('name', 'N/A')
                                            ))
                                            
                                except meraki.APIError as e:
                                    print(f"    Error getting VLAN {vlan['id']} details: {e}")
                                    
                        except meraki.APIError as e:
                            print(f"    Error getting VLANs for network {net['name']}: {e}")
                            
            except meraki.APIError as e:
                print(f"  Error getting networks for org {org['name']}: {e}")
                
    except meraki.APIError as e:
        print(f"Error accessing Meraki organizations: {e}")
    
    return reservations

def main():
    print("\nConnecting to Meraki dashboard...")
    dashboard = setup_meraki()
    
    print("Collecting DHCP reservations...")
    reservations = get_dhcp_reservations(dashboard)
    
    if not reservations:
        print("\nNo DHCP reservations found.")
        return
    
    # Sort reservations by site name and IP address
    reservations.sort(key=lambda x: (x.site, x.ip))
    
    # Generate report
    headers = ["Site", "VLAN ID", "VLAN Name", "MAC Address", "Reserved IP", "Description"]
    rows = [reservation.to_row() for reservation in reservations]
    
    print("\nDHCP Reservations Report")
    print("=" * 80)
    print(tabulate(rows, headers=headers, tablefmt="simple"))
    print(f"\nTotal reservations found: {len(reservations)}")

if __name__ == "__main__":
    main()
