#!/usr/bin/env python3
"""
Script: unifi_wap_credentials.py

Lists all UniFi Access Points (both online and offline) across all sites with their SSH credentials.
Displays AP name, IP address, status, and SSH connection details.

Requirements:
    pip install requests tabulate paramiko
"""

import sys
import getpass
import requests
import paramiko
import time
from typing import List, Dict, Tuple
from tabulate import tabulate
from urllib3.exceptions import InsecureRequestWarning

# Configuration
UNIFI_HOST = "https://unifi.limocar.int:8443"
USERNAME = "blos01"
VERIFY_SSL = False
SSH_TIMEOUT = 10  # seconds

# Suppress insecure HTTPS warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def ssh_get_status(ip: str, username: str, password: str) -> str:
    """SSH into AP and get status from info command"""
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(ip, username=username, password=password, timeout=SSH_TIMEOUT)
        
        # Wait a bit for the connection
        time.sleep(1)
        
        # Execute info command
        stdin, stdout, stderr = ssh.exec_command('info')
        output = stdout.read().decode()
        
        # Close connection
        ssh.close()
        
        # Extract status line
        for line in output.splitlines():
            if line.strip().startswith('Status:'):
                return line.strip()
        
        return "Status information not found"
    
    except Exception as e:
        return f"SSH Error: {str(e)}"

def login_unifi(session: requests.Session, password: str) -> bool:
    """Log into the UniFi Controller"""
    login_url = f"{UNIFI_HOST}/api/login"
    payload = {
        "username": USERNAME,
        "password": password
    }
    resp = session.post(login_url, json=payload, verify=VERIFY_SSL)
    return resp.status_code == 200

def get_sites(session: requests.Session) -> List[Dict]:
    """Get all sites from the controller"""
    url = UNIFI_HOST + "/api/self/sites"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    # Fallback for older controllers
    fallback_url = UNIFI_HOST + "/api/stat/sites"
    resp = session.get(fallback_url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    return []

def get_site_settings(session: requests.Session, site_name: str) -> Dict:
    """Get site settings including device authentication"""
    url = f"{UNIFI_HOST}/api/s/{site_name}/rest/setting"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    return []

def get_aps_for_site(session: requests.Session, site_name: str) -> List[Dict]:
    """Get all access points for a specific site"""
    url = f"{UNIFI_HOST}/api/s/{site_name}/stat/device"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        devices = resp.json().get("data", [])
        # Return all APs (type "uap"), regardless of state
        return [dev for dev in devices if dev.get("type") == "uap"]
    return []

def get_ap_status(ap: Dict) -> str:
    """Get formatted AP status with uptime if online"""
    state = ap.get("state", 0)
    if state == 1:
        uptime = ap.get("uptime", 0)
        days = uptime // (24 * 3600)
        hours = (uptime % (24 * 3600)) // 3600
        if days > 0:
            return f"Online ({days}d {hours}h)"
        return f"Online ({hours}h)"
    return "Offline"

def collect_ap_data(session: requests.Session) -> List[Dict]:
    """Collect AP data including SSH credentials across all sites"""
    ap_data = []
    sites = get_sites(session)
    
    for site in sites:
        site_name = site["name"]
        site_desc = site.get("desc", site_name)
        
        # Get site settings for SSH credentials
        settings = get_site_settings(session, site_name)
        ssh_settings = next((s for s in settings if s.get("key") == "mgmt"), {})
        
        # Get default SSH credentials from settings
        ssh_username = ssh_settings.get("x_ssh_username", "ubnt")
        ssh_password = ssh_settings.get("x_ssh_password", "ubnt")
        
        # Get APs for this site
        aps = get_aps_for_site(session, site_name)
        
        for ap in aps:
            status = get_ap_status(ap)
            ssh_status = ""
            
            # Only try SSH for online APs
            if not status.startswith("Offline") and ap.get("ip"):
                print(f"Connecting to {ap.get('name', 'Unknown')} ({ap['ip']})...")
                ssh_status = ssh_get_status(ap["ip"], ssh_username, ssh_password)
            
            ap_data.append({
                "site": site_desc,
                "name": ap.get("name", "Unnamed AP"),
                "model": ap.get("model", "Unknown"),
                "ip": ap.get("ip", "No IP"),
                "mac": ap.get("mac", "Unknown MAC"),
                "version": ap.get("version", "Unknown"),
                "status": status,
                "ssh_status": ssh_status,
                "ssh_username": ssh_username,
                "ssh_password": ssh_password,
                "last_seen": ap.get("last_seen", 0)
            })
    
    # Sort by site name and then by AP status (online first)
    return sorted(ap_data, key=lambda x: (x["site"], x["status"].startswith("Offline")))

def main():
    print(f"UniFi Controller: {UNIFI_HOST}")
    print(f"Logging in as user: {USERNAME}")
    
    # Get password
    password = getpass.getpass("Enter your admin password: ")
    
    # Setup session
    session = requests.Session()
    
    # Login
    if not login_unifi(session, password):
        print("[!] Login failed")
        sys.exit(1)
    
    # Collect AP data
    print("\nCollecting AP information...")
    ap_data = collect_ap_data(session)
    
    if not ap_data:
        print("[!] No APs found or insufficient permissions")
        sys.exit(0)
    
    # Count online/offline APs
    online_count = sum(1 for ap in ap_data if not ap["status"].startswith("Offline"))
    offline_count = len(ap_data) - online_count
    
    # Display results
    headers = ["Site", "AP Name", "Model", "IP Address", "MAC Address", "Status", "Version", "SSH Status", "SSH Username", "SSH Password"]
    table_data = [
        [
            ap["site"],
            ap["name"],
            ap["model"],
            ap["ip"],
            ap["mac"],
            ap["status"],
            ap["version"],
            ap["ssh_status"],
            ap["ssh_username"],
            ap["ssh_password"]
        ]
        for ap in ap_data
    ]
    
    print("\nAccess Point Information:")
    print(tabulate(table_data, headers=headers, tablefmt="simple"))
    print(f"\nTotal APs: {len(ap_data)} (Online: {online_count}, Offline: {offline_count})")

if __name__ == "__main__":
    main()
