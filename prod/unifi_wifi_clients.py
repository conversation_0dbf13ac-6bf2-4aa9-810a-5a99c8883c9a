#!/usr/bin/env python3
"""
Script: unifi_wifi_clients.py

Lists all WiFi clients connected across all UniFi sites, organized by SSID.
Displays site name, client name, connected AP, SSID, and IP address.
Updates every 30 seconds.

Requirements:
    pip install requests tabulate
"""

import sys
import time
import getpass
import requests
from typing import List, Dict, Tuple
from tabulate import tabulate
from collections import defaultdict
from urllib3.exceptions import InsecureRequestWarning
from datetime import datetime

# Suppress insecure HTTPS warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# Configuration
UNIFI_HOST = "https://serv-uni01.limocar.int:8443"
USERNAME = "blos01"
VERIFY_SSL = False
UPDATE_INTERVAL = 30  # seconds

class UnifiController:
    def __init__(self, host: str, username: str, password: str, verify_ssl: bool = True):
        self.host = host
        self.username = username
        self.password = password  # Store password for reconnection if needed
        self.verify_ssl = verify_ssl
        self.session = requests.Session()
        
        if not self.login():
            raise ConnectionError("Failed to login to UniFi Controller")
    
    def login(self) -> bool:
        """Login to UniFi Controller"""
        login_url = f"{self.host}/api/login"
        payload = {
            "username": self.username,
            "password": self.password
        }
        resp = self.session.post(login_url, json=payload, verify=self.verify_ssl)
        return resp.status_code == 200
    
    def refresh_session(self) -> bool:
        """Refresh session if needed"""
        try:
            # Test session with a simple API call
            self.get_sites()
            return True
        except Exception:
            # If failed, try to login again
            return self.login()
    
    def get_sites(self) -> List[Dict]:
        """Get all sites from the controller"""
        # Try primary API endpoint
        resp = self.session.get(f"{self.host}/api/self/sites", verify=self.verify_ssl)
        if resp.status_code == 200:
            return resp.json().get("data", [])
        
        # Fallback for older controllers
        resp = self.session.get(f"{self.host}/api/stat/sites", verify=self.verify_ssl)
        if resp.status_code == 200:
            return resp.json().get("data", [])
        
        return []
    
    def get_devices(self, site_name: str) -> List[Dict]:
        """Get all devices for a specific site"""
        resp = self.session.get(
            f"{self.host}/api/s/{site_name}/stat/device",
            verify=self.verify_ssl
        )
        return resp.json().get("data", []) if resp.status_code == 200 else []
    
    def get_wireless_clients(self, site_name: str) -> List[Dict]:
        """Get all wireless clients for a specific site"""
        resp = self.session.get(
            f"{self.host}/api/s/{site_name}/stat/sta",
            verify=self.verify_ssl
        )
        if resp.status_code == 200:
            clients = resp.json().get("data", [])
            return [c for c in clients if c.get("essid")]
        return []

class WifiClient:
    def __init__(self, site: str, name: str, ap_name: str, ip: str):
        self.site = site
        self.name = name
        self.ap_name = ap_name
        self.ip = ip
    
    def to_list(self) -> List[str]:
        return [self.site, self.name, self.ap_name, self.ip]

class ClientReport:
    def __init__(self):
        self.clients_by_ssid = defaultdict(list)
        self.total_clients = 0
    
    def add_client(self, ssid: str, client: WifiClient):
        self.clients_by_ssid[ssid].append(client)
        self.total_clients += 1
    
    def sort_clients(self):
        """Sort clients within each SSID by site and client name"""
        for ssid in self.clients_by_ssid:
            self.clients_by_ssid[ssid].sort(key=lambda x: (x.site, x.name))
    
    def get_ssid_summary(self) -> List[List]:
        """Generate summary statistics for each SSID"""
        summary = []
        for ssid, clients in self.clients_by_ssid.items():
            client_count = len(clients)
            percentage = (client_count / self.total_clients * 100) if self.total_clients > 0 else 0
            summary.append([ssid, client_count, f"{percentage:.1f}%"])
        
        return sorted(summary, key=lambda x: x[1], reverse=True)
    
    def print_report(self):
        """Print the complete client report"""
        headers = ["Site", "Client Name", "Connected AP", "IP Address"]
        
        print("\nWiFi Clients by SSID:")
        print("=" * 100)
        
        # Print client details for each SSID
        for ssid, clients in sorted(self.clients_by_ssid.items()):
            print(f"\nSSID: {ssid}")
            print("-" * 100)
            client_rows = [client.to_list() for client in clients]
            print(tabulate(client_rows, headers=headers, tablefmt="simple"))
        
        print("\n" + "=" * 100)
        
        # Print SSID summary
        print("\nClients per SSID:")
        print("-" * 40)
        
        summary_headers = ["SSID", "Clients", "% of Total"]
        print(tabulate(
            self.get_ssid_summary(),
            headers=summary_headers,
            tablefmt="simple",
            numalign="right"
        ))
        
        # Print total statistics
        print("\nTotal Statistics:")
        print(f"Total SSIDs:    {len(self.clients_by_ssid)}")
        print(f"Total Clients:  {self.total_clients}")

def collect_client_data(controller: UnifiController) -> ClientReport:
    """Collect all client data from the controller"""
    report = ClientReport()
    
    for site in controller.get_sites():
        site_name = site["name"]
        site_desc = site.get("desc", site_name)
        
        # Get AP name mapping
        devices = controller.get_devices(site_name)
        ap_names = {
            dev.get("mac"): dev.get("name", "Unknown AP")
            for dev in devices
        }
        
        # Get and process wireless clients
        for client in controller.get_wireless_clients(site_name):
            ssid = client.get("essid", "Unknown SSID")
            ap_name = ap_names.get(client.get("ap_mac"), "Unknown AP")
            
            wifi_client = WifiClient(
                site=site_desc,
                name=client.get("name", client.get("hostname", "Unknown Client")),
                ap_name=ap_name,
                ip=client.get("ip", "No IP")
            )
            
            report.add_client(ssid, wifi_client)
    
    report.sort_clients()
    return report

def clear_screen():
    """Clear the terminal screen"""
    print("\033[2J\033[H", end="")

def main():
    print(f"UniFi Controller: {UNIFI_HOST}")
    print(f"Logging in as user: {USERNAME}")
    
    try:
        # Initialize controller connection
        controller = UnifiController(
            host=UNIFI_HOST,
            username=USERNAME,
            password=getpass.getpass("Enter your admin password: "),
            verify_ssl=VERIFY_SSL
        )
        
        print("\nStarting monitoring loop. Press Ctrl+C to exit.")
        time.sleep(2)
        
        while True:
            try:
                clear_screen()
                print(f"UniFi WiFi Client Monitor - Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Controller: {UNIFI_HOST}")
                print("-" * 80)
                
                # Ensure session is still valid
                if not controller.refresh_session():
                    print("Session expired. Reconnecting...")
                    continue
                
                # Collect and display client data
                report = collect_client_data(controller)
                report.print_report()
                
                print(f"\nNext update in {UPDATE_INTERVAL} seconds. Press Ctrl+C to exit.")
                time.sleep(UPDATE_INTERVAL)
                
            except requests.exceptions.RequestException as e:
                print(f"\nNetwork error: {e}")
                print(f"Retrying in {UPDATE_INTERVAL} seconds...")
                time.sleep(UPDATE_INTERVAL)
                
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
