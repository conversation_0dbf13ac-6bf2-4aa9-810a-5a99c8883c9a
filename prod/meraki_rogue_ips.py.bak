#!/usr/bin/env python3
"""
Script: meraki_rogue_ips.py

Checks for rogue IPs across all Meraki networks by:
1. Collecting all VLANs and their subnets
2. Getting client ARP entries
3. Identifying IPs that don't belong to their VLAN's subnet

Requirements:
    pip install meraki ipaddress tabulate
"""

import sys
from typing import Dict, List, NamedTuple
import ipaddress
import meraki
from tabulate import tabulate
from datetime import datetime

# Configuration
MERAKI_API_KEY = "****************************************"

class RogueIP(NamedTuple):
    org_name: str
    network_name: str
    vlan_id: int
    vlan_name: str
    vlan_subnet: str
    client_ip: str
    client_mac: str
    client_description: str

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def get_network_vlans(dashboard: meraki.DashboardAPI, network_id: str) -> List[Dict]:
    """Get VLANs for a specific network"""
    try:
        return dashboard.appliance.getNetworkApplianceVlans(network_id)
    except meraki.APIError as e:
        print(f"Error getting VLANs for network {network_id}: {e}")
        return []

def get_client_arp(dashboard: meraki.DashboardAPI, network_id: str) -> List[Dict]:
    """Get client ARP entries for a network"""
    try:
        # Increase timespan to ensure we catch all clients
        return dashboard.networks.getNetworkClients(
            networkId=network_id,
            total_pages='all',
            timespan=86400  # Look at last 24 hours instead of 1 hour
        )
    except meraki.APIError as e:
        print(f"Error getting ARP entries for network {network_id}: {e}")
        return []

def check_rogue_ips(dashboard: meraki.DashboardAPI) -> List[RogueIP]:
    """Check for rogue IPs across all Meraki networks"""
    rogue_ips = []
    
    try:
        orgs = dashboard.organizations.getOrganizations()
        
        for org in orgs:
            print(f"\nProcessing organization: {org['name']}")
            
            try:
                networks = dashboard.organizations.getOrganizationNetworks(org['id'])
            except meraki.APIError as e:
                print(f"Error getting networks for org {org['name']}: {e}")
                continue

            for net in networks:
                print(f"  Checking network: {net['name']}")
                
                vlans = get_network_vlans(dashboard, net['id'])
                if not vlans:
                    continue

                # Create VLAN subnet mapping with more detailed debug output
                vlan_subnets = {}
                for vlan in vlans:
                    try:
                        if vlan.get('subnet'):
                            subnet = ipaddress.ip_network(vlan['subnet'], strict=False)
                            vlan_subnets[str(vlan['id'])] = {  # Convert VLAN ID to string
                                'network': subnet,
                                'name': vlan.get('name', 'Unnamed VLAN')
                            }
                            print(f"    VLAN {vlan['id']} ({vlan.get('name', 'Unnamed')}): {subnet}")
                    except ValueError as e:
                        print(f"    Error parsing subnet for VLAN {vlan['id']}: {e}")
                        continue

                clients = get_client_arp(dashboard, net['id'])
                print(f"    Found {len(clients)} clients")
                
                for client in clients:
                    try:
                        if not (client.get('ip') and client.get('vlan') is not None):
                            continue
                            
                        client_ip = ipaddress.ip_address(client['ip'])
                        vlan_id = str(client['vlan'])  # Ensure VLAN ID is string
                        
                        if vlan_id in vlan_subnets:
                            vlan_network = vlan_subnets[vlan_id]['network']
                            if client_ip not in vlan_network:
                                print(f"    Found rogue IP {client_ip} in VLAN {vlan_id} (subnet: {vlan_network})")
                                rogue_ips.append(RogueIP(
                                    org_name=org['name'],
                                    network_name=net['name'],
                                    vlan_id=vlan_id,
                                    vlan_name=vlan_subnets[vlan_id]['name'],
                                    vlan_subnet=str(vlan_network),
                                    client_ip=client['ip'],
                                    client_mac=client.get('mac', 'Unknown'),
                                    client_description=client.get('description', 'No description')
                                ))
                    except ValueError as e:
                        print(f"    Error processing client IP {client.get('ip')}: {e}")
                        continue

    except meraki.APIError as e:
        print(f"Error accessing Meraki organizations: {e}")

    return rogue_ips

def display_rogue_ips(rogue_ips: List[RogueIP]) -> str:
    """Format rogue IPs into a table"""
    headers = {
        'network_name': 'SD-WAN Name',
        'vlan_id': 'VLAN ID',
        'vlan_name': 'VLAN Name',
        'vlan_subnet': 'VLAN Subnet',
        'client_ip': 'Rogue IP',
        'client_mac': 'MAC Address',
        'client_description': 'Description'
    }
    
    # Filter out org_name from the display
    rows = []
    for ip in rogue_ips:
        row = ip._asdict()
        del row['org_name']  # Remove organization from display
        rows.append(row)
    
    return tabulate(
        rows,
        headers=headers,
        tablefmt='simple',  # Changed from 'grid' to 'simple'
        numalign='left',
        stralign='left'
    )

def main():
    print("\nConnecting to Meraki dashboard...")
    dashboard = setup_meraki()
    
    print("Checking for rogue IPs across all networks...")
    rogue_ips = check_rogue_ips(dashboard)
    
    if not rogue_ips:
        print("\nNo rogue IPs found.")
        return
    
    print(f"\nFound {len(rogue_ips)} rogue IPs:")
    print(display_rogue_ips(rogue_ips))
    
    # Save results to file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'rogue_ips_{timestamp}.txt'
    
    with open(filename, 'w') as f:
        f.write(display_rogue_ips(rogue_ips))
    
    print(f"\nResults saved to {filename}")

if __name__ == '__main__':
    main()
