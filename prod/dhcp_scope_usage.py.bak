#!/usr/bin/env python3
"""
Script: dhcp_scope_report.py

Generates a comprehensive DHCP scope usage report for Meraki networks.
Shows site, VLAN, total scope size, used IPs, and usage percentage.

Requirements:
    pip install meraki tabulate
"""

from typing import List, Any
import ipaddress
import meraki
from tabulate import tabulate

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

class DHCPScopeInfo:
    def __init__(self, site: str, vlan_id: str, subnet: str, total_ips: int, used_ips: int):
        self.site = site
        self.vlan_id = vlan_id
        self.subnet = subnet
        self.total_ips = total_ips
        self.used_ips = used_ips
        self.usage_percent = (used_ips / total_ips * 100) if total_ips > 0 else 0

    def to_row(self) -> List[Any]:
        return [
            self.site,
            self.vlan_id,
            self.subnet,
            self.total_ips,
            self.used_ips,
            f"{self.usage_percent:.1f}%"
        ]

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def get_meraki_scopes(dashboard: meraki.DashboardAPI) -> List[DHCPScopeInfo]:
    """Collect DHCP scope information from Meraki networks"""
    scopes = []

    try:
        # Get all organizations
        orgs = dashboard.organizations.getOrganizations()

        for org in orgs:
            print(f"Processing Meraki organization: {org['name']}")

            # Get all networks in the organization
            networks = dashboard.organizations.getOrganizationNetworks(org['id'])

            for net in networks:
                try:
                    # Check if this is an appliance network
                    if 'appliance' not in net.get('productTypes', []):
                        continue

                    # Get clients for IP usage calculation
                    clients = dashboard.networks.getNetworkClients(
                        net['id'],
                        timespan=3600  # Last hour
                    )

                    try:
                        # Try to get VLANs first
                        vlans = dashboard.appliance.getNetworkApplianceVlans(net['id'])

                        for vlan in vlans:
                            if vlan.get('dhcpHandling') == 'Run a DHCP server':
                                subnet = vlan.get('subnet')
                                if not subnet:
                                    continue

                                # Calculate scope size and usage
                                network = ipaddress.ip_network(subnet)
                                total_ips = network.num_addresses - 2  # Subtract network and broadcast

                                # Count clients in this subnet
                                used_ips = len([c for c in clients if c.get('ip') and
                                              ipaddress.ip_address(c['ip']) in network])

                                scopes.append(DHCPScopeInfo(
                                    site=net['name'],  # Changed: Only use network name
                                    vlan_id=str(vlan.get('id', 'Default')),
                                    subnet=subnet,
                                    total_ips=total_ips,
                                    used_ips=used_ips
                                ))

                    except meraki.APIError as e:
                        if '400 Bad Request' in str(e) and 'VLANs are not enabled' in str(e):
                            # For networks without VLANs, check the default DHCP
                            try:
                                # Get single VLAN settings for networks without VLAN support
                                single_vlan = dashboard.appliance.getNetworkApplianceSettings(net['id'])
                                if single_vlan.get('dhcpHandling') == 'Run a DHCP server':
                                    subnet = single_vlan.get('subnet')
                                    if subnet:
                                        network = ipaddress.ip_network(subnet)
                                        total_ips = network.num_addresses - 2

                                        used_ips = len([c for c in clients if c.get('ip') and
                                                      ipaddress.ip_address(c['ip']) in network])

                                        scopes.append(DHCPScopeInfo(
                                            site=net['name'],  # Changed: Only use network name
                                            vlan_id="Default",
                                            subnet=subnet,
                                            total_ips=total_ips,
                                            used_ips=used_ips
                                        ))
                            except meraki.APIError as dhcp_error:
                                print(f"Error checking DHCP for network {net['name']}: {dhcp_error}")
                        else:
                            print(f"Error processing VLANs for network {net['name']}: {e}")

                except meraki.APIError as e:
                    print(f"Error processing network {net['name']}: {e}")
                    continue

    except meraki.APIError as e:
        print(f"Error accessing Meraki organizations: {e}")

    return scopes



def main():
    all_scopes = []

    # Get Meraki DHCP scopes
    print("\nCollecting Meraki DHCP information...")
    dashboard = setup_meraki()
    meraki_scopes = get_meraki_scopes(dashboard)
    all_scopes.extend(meraki_scopes)

    if not all_scopes:
        print("No DHCP scopes found.")
        return

    # Sort scopes by usage percentage (ascending)
    all_scopes.sort(key=lambda x: x.usage_percent)

    # Generate report
    headers = ["Site", "VLAN", "Subnet", "Total IPs", "Used IPs", "Usage"]
    rows = [scope.to_row() for scope in all_scopes]

    # Calculate column totals
    site_count = len(set(scope.site for scope in all_scopes))
    vlan_count = len(set(scope.vlan_id for scope in all_scopes))
    subnet_count = len(set(scope.subnet for scope in all_scopes))
    total_ips_sum = sum(scope.total_ips for scope in all_scopes)
    used_ips_sum = sum(scope.used_ips for scope in all_scopes)
    overall_usage_percent = (used_ips_sum / total_ips_sum * 100) if total_ips_sum > 0 else 0

    # Add a blank row and then the totals row
    rows.append(["" for _ in range(len(headers))])
    rows.append([
        f"Sites: {site_count}",
        f"VLANs: {vlan_count}",
        f"Subnets: {subnet_count}",
        total_ips_sum,
        used_ips_sum,
        f"{overall_usage_percent:.1f}%"
    ])

    print("\nDHCP Scope Usage Report")
    print("=" * 80)
    print(tabulate(rows, headers=headers, tablefmt="simple"))

    # Print detailed column statistics
    print("\nColumn Statistics:")
    print(f"  Sites: {site_count} unique sites")
    print(f"  VLANs: {vlan_count} unique VLANs")
    print(f"  Subnets: {subnet_count} unique subnets")
    print(f"  Total IPs: {total_ips_sum} addresses available across all scopes")
    print(f"  Used IPs: {used_ips_sum} addresses in use across all scopes")
    print(f"  Overall Usage: {overall_usage_percent:.1f}% of all available addresses are in use")

    # Add usage distribution statistics
    usage_ranges = {
        "0-25%": 0,
        "26-50%": 0,
        "51-75%": 0,
        "76-90%": 0,
        "91-100%": 0
    }

    for scope in all_scopes:
        if scope.usage_percent <= 25:
            usage_ranges["0-25%"] += 1
        elif scope.usage_percent <= 50:
            usage_ranges["26-50%"] += 1
        elif scope.usage_percent <= 75:
            usage_ranges["51-75%"] += 1
        elif scope.usage_percent <= 90:
            usage_ranges["76-90%"] += 1
        else:
            usage_ranges["91-100%"] += 1

    print("\nUsage Distribution:")
    for range_name, count in usage_ranges.items():
        percentage = (count / len(all_scopes) * 100) if all_scopes else 0
        print(f"  {range_name}: {count} scopes ({percentage:.1f}%)")

if __name__ == "__main__":
    main()
