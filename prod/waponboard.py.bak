#!/usr/bin/env python3

import sys
import time
import subprocess
import paramiko
import threading
from queue import Queue
from typing import List

def expand_ip_range(ip_arg):
    """
    Expand IP addresses in various formats into a list of IPs:
      - Single IP: "***********" -> ["***********"]
      - IP range: "***********-7" -> ["***********", "***********", ... "***********"]
      - CIDR: "*************/26" -> ["*************", ... "*************"]
    """
    import ipaddress
    
    # Handle CIDR notation
    if '/' in ip_arg:
        try:
            network = ipaddress.ip_network(ip_arg.strip())
            return [str(ip) for ip in network.hosts()]
        except ValueError as e:
            raise ValueError(f"Invalid CIDR format: {e}")

    # Handle single IP
    if '-' not in ip_arg:
        try:
            # Validate IP format
            ipaddress.ip_address(ip_arg.strip())
            return [ip_arg.strip()]
        except ValueError as e:
            raise ValueError(f"Invalid IP format: {e}")
    
    # Handle IP range
    base, last_octet_range = ip_arg.split('-')
    base_parts = base.strip().split('.')
    if len(base_parts) != 4:
        raise ValueError(f"Invalid IP format: {base}")

    start_octet = int(base_parts[3])
    end_octet = int(last_octet_range.strip())

    if end_octet < start_octet or not (0 <= start_octet <= 255) or not (0 <= end_octet <= 255):
        raise ValueError(f"Invalid IP range: {ip_arg}")

    ip_list = []
    for octet in range(start_octet, end_octet + 1):
        ip_list.append(f"{base_parts[0]}.{base_parts[1]}.{base_parts[2]}.{octet}")
    return ip_list

def ping_host(ip):
    """
    Returns True if the IP responds to a single ping, False otherwise.
    -c 1 sends one ping, -W 1 sets a 1-second timeout.
    """
    result = subprocess.call(
        ["ping", "-c", "2", "-W", "1", ip],
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL
    )
    return (result == 0)

def ssh_and_inform(ip, username="ubnt", password="ubnt"):
    """
    SSH into the device and run syswrapper.sh set-inform.
    Returns True if successful, False otherwise.
    """
    ssh_client = paramiko.SSHClient()
    # Accept unknown host keys automatically:
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh_client.connect(ip, username=username, password=password, timeout=5)
        command = "syswrapper.sh set-inform http://***********:8080/inform\n"
        stdin, stdout, stderr = ssh_client.exec_command(command)

        exit_code = stdout.channel.recv_exit_status()
        ssh_client.close()

        return (exit_code == 0)
    except Exception as e:
        print(f"SSH error for {ip}: {e}")
        return False

def process_target(target: str, results_queue: Queue):
    """
    Process a single target (IP range, CIDR, or single IP) in its own thread.
    """
    try:
        ips = expand_ip_range(target)
        if not ips:
            return

        # Keep track of unreachable IPs for this target
        unreachable_ips = ips[:]

        # Initial attempt
        for ip in ips:
            if ping_host(ip):
                if ssh_and_inform(ip):
                    print(f"[+] {ip}: set-inform succeeded.")
                    results_queue.put((ip, True))
                else:
                    print(f"[-] {ip}: SSH failed or set-inform command returned error.")
                unreachable_ips.remove(ip)
            else:
                print(f"[-] {ip}: no ping response.")

        # Retry loop for those that didn't respond to ping
        while unreachable_ips:
            time.sleep(5)
            still_unreachable = []
            for ip in unreachable_ips:
                if ping_host(ip):
                    if ssh_and_inform(ip):
                        print(f"[+] {ip}: set-inform succeeded.")
                        results_queue.put((ip, True))
                    else:
                        print(f"[-] {ip}: SSH failed or set-inform command returned error.")
                else:
                    still_unreachable.append(ip)
            unreachable_ips = still_unreachable
            if unreachable_ips:
                print(f"[*] Still unreachable in {target}: {unreachable_ips}")

    except ValueError as ve:
        print(f"Error in target {target}: {ve}")
    except Exception as e:
        print(f"Unexpected error processing {target}: {e}")

def main():
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <ip_address1> [ip_address2 ...]")
        print(f"Examples:")
        print(f"  {sys.argv[0]} *************-80                          # IP range")
        print(f"  {sys.argv[0]} ***********                               # Single IP")
        print(f"  {sys.argv[0]} *************/26                          # CIDR notation")
        print(f"  {sys.argv[0]} ***********-7 *************/26 ***********  # Multiple targets")
        sys.exit(1)

    targets = sys.argv[1:]
    threads: List[threading.Thread] = []
    results_queue: Queue = Queue()

    # Create and start a thread for each target
    for target in targets:
        thread = threading.Thread(
            target=process_target,
            args=(target, results_queue),
            name=f"Thread-{target}"
        )
        threads.append(thread)
        thread.start()
        print(f"Started processing {target}")

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    # Process results
    successful_ips = []
    while not results_queue.empty():
        ip, success = results_queue.get()
        if success:
            successful_ips.append(ip)

    # Final summary
    print("\nProcessing complete!")
    if successful_ips:
        print(f"Successfully configured {len(successful_ips)} devices:")
        for ip in successful_ips:
            print(f"  - {ip}")
    else:
        print("No devices were successfully configured.")

if __name__ == "__main__":
    main()

