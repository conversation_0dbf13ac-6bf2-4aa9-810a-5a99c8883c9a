#!/usr/bin/env python3
"""
Script: unifi_netbox_sync.py

Syncs UniFi access points to Netbox, creating devices and assigning IP addresses.
"""

import sys
import getpass
import requests
import pynetbox
import ipaddress
from typing import List, Dict, Optional
from tabulate import tabulate
from urllib3.exceptions import InsecureRequestWarning

# Suppress SSL verification warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
from urllib3.exceptions import InsecureRequestWarning

# Suppress SSL verification warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# UniFi Configuration
UNIFI_HOST = "https://unifi.limocar.int:8443"
UNIFI_USERNAME = "blos01"
VERIFY_SSL = False

# Netbox Configuration
NETBOX_URL = "https://ansj7013.cloud.netboxapp.com"
NETBOX_TOKEN = "vXBuZYQp1X80lim}trYdenPbcW0x#nvf+1gOry#X"

def login_unifi(session: requests.Session, password: str) -> bool:
    """Log into the UniFi Controller"""
    login_url = f"{UNIFI_HOST}/api/login"
    payload = {
        "username": UNIFI_USERNAME,
        "password": password
    }
    resp = session.post(login_url, json=payload, verify=VERIFY_SSL)
    return resp.status_code == 200

def get_sites(session: requests.Session) -> List[Dict]:
    """Get all sites from the controller"""
    url = UNIFI_HOST + "/api/self/sites"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    # Fallback for older controllers
    fallback_url = UNIFI_HOST + "/api/stat/sites"
    resp = session.get(fallback_url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    return []

def get_aps_for_site(session: requests.Session, site_name: str) -> List[Dict]:
    """Get all access points for a specific site"""
    url = f"{UNIFI_HOST}/api/s/{site_name}/stat/device"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        devices = resp.json().get("data", [])
        # Filter for only APs (type "uap")
        return [dev for dev in devices if dev.get("type") == "uap"]
    return []

def get_all_aps(session: requests.Session) -> List[Dict]:
    """Get all APs from all sites"""
    ap_data = []
    sites = get_sites(session)
    
    for site in sites:
        site_name = site["name"]
        aps = get_aps_for_site(session, site_name)
        
        for ap in aps:
            ap_data.append({
                "name": ap.get("name", "Unnamed AP"),
                "model": ap.get("model", "Unknown Model"),
                "ip_address": ap.get("ip", ""),
                "mac": ap.get("mac", ""),
                "version": ap.get("version", "Unknown Version")
            })
    
    return ap_data

def setup_netbox() -> pynetbox.api:
    """Initialize Netbox API"""
    nb = pynetbox.api(
        url=NETBOX_URL,
        token=NETBOX_TOKEN
    )
    nb.http_session.verify = True
    return nb

def find_site_by_prefix(nb: pynetbox.api, ip_address: str) -> Optional[object]:
    """Find Netbox site by searching for the prefix containing the IP"""
    try:
        # Convert IP string to IP address object
        ip = ipaddress.ip_address(ip_address)
        
        # Get all prefixes
        prefixes = nb.ipam.prefixes.all()
        for prefix in prefixes:
            network = ipaddress.ip_network(prefix.prefix)
            if ip in network:
                return prefix.site
    except Exception as e:
        print(f"Error finding site for IP {ip_address}: {e}")
    return None

def sync_devices(nb: pynetbox.api, ap_data: List[Dict], site: object) -> None:
    """Synchronize devices between UniFi and Netbox"""
    try:
        # Get all existing WAPs in Netbox for this site
        existing_waps = nb.dcim.devices.filter(
            site_id=site.id,
            role_id=nb.dcim.device_roles.get(slug="wireless-access-point").id
        )
        
        # Create sets for comparison
        unifi_macs = {ap["mac"].lower() for ap in ap_data}
        unifi_ips = {ap["ip_address"] for ap in ap_data}
        netbox_macs = {wap.serial.lower() for wap in existing_waps if wap.serial}
        
        # Get all existing IP addresses in Netbox
        existing_ips = set()
        for ip in nb.ipam.ip_addresses.filter(mask_length=32):
            existing_ips.add(str(ip).split('/')[0])
        
        # Delete WAPs that exist in Netbox but not in UniFi
        for wap in existing_waps:
            if not wap.serial or wap.serial.lower() not in unifi_macs:
                print(f"Deleting WAP {wap.name} - not found in UniFi")
                wap.delete()
        
        # Sort APs by IP address
        ap_data.sort(key=lambda x: ipaddress.ip_address(x["ip_address"]))
        
        # Create or update WAPs
        for index, ap in enumerate(ap_data, 1):
            mac = ap["mac"].lower()
            ip = ap["ip_address"]
            
            # Skip if MAC or IP already exists in Netbox
            if mac in netbox_macs:
                print(f"Skipping WAP with MAC {ap['mac']} - already exists in Netbox")
                continue
                
            if ip in existing_ips:
                print(f"Skipping WAP with IP {ip} - IP already exists in Netbox")
                continue

            # Generate the proper name with padding
            device_name = f"{site.name}-WAP-{index:02d}"
            
            # Create new device
            print(f"Creating new WAP {device_name}")
            create_netbox_device(nb, ap, site, device_name)

        # Delete WAPs without IP addresses
        print("\nChecking for WAPs without IP addresses...")
        waps_without_ip = nb.dcim.devices.filter(
            site_id=site.id,
            role_id=nb.dcim.device_roles.get(slug="wireless-access-point").id,
            has_primary_ip=False
        )
        
        for wap in waps_without_ip:
            print(f"Deleting WAP {wap.name} - no IP address assigned")
            wap.delete()

        # Delete WAPs without IP addresses
        print("\nChecking for WAPs without IP addresses...")
        waps_without_ip = nb.dcim.devices.filter(
            site_id=site.id,
            role_id=nb.dcim.device_roles.get(slug="wireless-access-point").id,
            has_primary_ip=False
        )
        
        for wap in waps_without_ip:
            print(f"Deleting WAP {wap.name} - no IP address assigned")
            wap.delete()

    except Exception as e:
        print(f"Error synchronizing devices: {e}")

def create_netbox_device(nb: pynetbox.api, ap_info: Dict, site: object, device_name: str) -> Optional[object]:
    """Create device in Netbox"""
    try:
        # Double-check IP address doesn't exist (defensive programming)
        ip_with_mask = f"{ap_info['ip_address']}/32"
        existing_ip = nb.ipam.ip_addresses.get(address=ip_with_mask)
        if existing_ip:
            print(f"Skipping WAP creation - IP {ap_info['ip_address']} already exists in Netbox")
            return None

        # Get or create device role
        role = nb.dcim.device_roles.get(slug="wireless-access-point")
        if not role:
            role = nb.dcim.device_roles.create(
                name="Wireless Access Point",
                slug="wireless-access-point",
                color="9e9e9e",
                vm_role=False,
                description="UniFi Access Points"
            )

        # Get or create manufacturer
        manufacturer = nb.dcim.manufacturers.get(slug="ubiquiti")
        if not manufacturer:
            manufacturer = nb.dcim.manufacturers.create(
                name="Ubiquiti",
                slug="ubiquiti",
                description="Ubiquiti Networks"
            )

        # Get or create device type
        model_slug = ap_info["model"].lower().replace(" ", "-")
        device_type = nb.dcim.device_types.get(slug=model_slug)
        if not device_type:
            device_type = nb.dcim.device_types.create(
                manufacturer=manufacturer.id,
                model=ap_info["model"],
                slug=model_slug,
                u_height=0,
                is_full_depth=False
            )

        # Create device
        device = nb.dcim.devices.create(
            name=device_name,
            device_type=device_type.id,
            role=role.id,
            site=site.id,
            status="active",
            serial=ap_info["mac"]
        )

        # Create management interface
        mgmt_interface = nb.dcim.interfaces.create(
            device=device.id,
            name="Management",
            type="1000base-t",
            mgmt_only=True
        )

        # Create primary IP
        ip_address = nb.ipam.ip_addresses.create(
            address=f"{ap_info['ip_address']}/32",
            status="active",
            assigned_object_type="dcim.interface",
            assigned_object_id=mgmt_interface.id
        )

        # Set as primary IP
        device.primary_ip4 = ip_address.id
        device.save()

        print(f"Created device {device_name} with IP {ap_info['ip_address']}")
        return device

    except Exception as e:
        print(f"Error creating device {device_name}: {e}")
        return None

def main():
    print(f"UniFi Controller: {UNIFI_HOST}")
    print(f"Logging in as user: {UNIFI_USERNAME}")
    
    # Get UniFi password
    password = getpass.getpass("Enter UniFi password: ")
    
    # Create session and login
    session = requests.Session()
    if not login_unifi(session, password):
        print("Failed to login to UniFi Controller")
        sys.exit(1)
    
    # Initialize Netbox
    print("\nInitializing Netbox connection...")
    nb = setup_netbox()
    
    # Get all APs from UniFi
    print("\nGetting APs from UniFi...")
    ap_data = get_all_aps(session)
    print(f"Found {len(ap_data)} APs")
    
    # Group APs by site (based on IP prefix)
    print("\nGrouping APs by site...")
    site_aps = {}
    for ap in ap_data:
        site = find_site_by_prefix(nb, ap["ip_address"])
        if site:
            if site.name not in site_aps:
                site_aps[site.name] = []
            site_aps[site.name].append(ap)
        else:
            print(f"Could not find site for AP {ap['name']} ({ap['ip_address']})")
    
    # Sync devices for each site
    print("\nSyncing devices...")
    for site_name, aps in site_aps.items():
        print(f"\nProcessing site: {site_name}")
        site = nb.dcim.sites.get(name=site_name)
        if site:
            sync_devices(nb, aps, site)
        else:
            print(f"Site {site_name} not found in Netbox")
    
    print("\nSync completed")

if __name__ == "__main__":
    main()
