#!/usr/bin/env python3
"""
Script: meraki_subnet_overlap.py

Analyzes VLAN subnets across Meraki organizations and networks to detect overlaps.
Only checks subnets configured in VLANs (ignores device subnets).
Excludes private subnets in *********/8 and ***********/16 ranges.

Requirements:
    pip install meraki tabulate
"""

import sys
from datetime import datetime
from typing import Dict, List, NamedTuple
import ipaddress
import meraki
from tabulate import tabulate

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

# Define excluded networks
EXCLUDED_NETWORKS = [
    ipaddress.ip_network("*********/8"),
    ipaddress.ip_network("***********/16")
]

EXCLUDED_VLAN_NAMES = [
    "Wifi Guest",
    "Fleet Wifi"
]

class SubnetInfo(NamedTuple):
    org_name: str
    net_name: str
    vlan_id: str
    subnet: str
    vlan_name: str
    network: ipaddress.IPv4Network
    dhcp_leases: int = 0  # Add default value for DHCP leases
    vpn_status: str = "Disabled"  # New field for VPN status

class OverlapInfo(NamedTuple):
    subnet1: SubnetInfo
    subnet2: SubnetInfo

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def is_excluded_subnet(network: ipaddress.IPv4Network) -> bool:
    """Check if network falls within excluded ranges"""
    return any(network.overlaps(excluded) for excluded in EXCLUDED_NETWORKS)

def get_dhcp_leases(dashboard: meraki.DashboardAPI, network_id: str, subnet: ipaddress.IPv4Network) -> int:
    """Get number of DHCP leases for a subnet"""
    try:
        # Get clients active in the last hour
        clients = dashboard.networks.getNetworkClients(
            networkId=network_id,
            timespan=3600  # Last hour
        )
        
        # Count clients with IPs in our subnet
        lease_count = len([
            client for client in clients 
            if client.get('ip') and ipaddress.ip_address(client['ip']) in subnet
        ])
        
        return lease_count
    except meraki.APIError as e:
        print(f"    Warning: Could not get DHCP leases: {e}")
        return 0

def get_vpn_subnets(dashboard: meraki.DashboardAPI, network_id: str) -> Dict[str, str]:
    """Get VPN status for each subnet"""
    vpn_status = {}
    try:
        vpn_config = dashboard.appliance.getNetworkApplianceVpnSiteToSiteVpn(network_id)
        
        # If VPN is not enabled globally, mark all subnets as "VPN Off"
        if not vpn_config.get('enabled', False):
            return vpn_status  # Empty dict - will default to "VPN Off"
            
        # Check subnets in both hubs and spokes
        for subnet in vpn_config.get('subnets', []):
            local_subnet = subnet.get('localSubnet')
            if local_subnet:
                vpn_status[local_subnet] = "Enabled" if subnet.get('useVpn', False) else "Disabled"
                
        return vpn_status
    except meraki.APIError as e:
        print(f"    Warning: Could not get VPN subnets: {e}")
        return vpn_status

def get_vlan_subnets(dashboard: meraki.DashboardAPI) -> List[SubnetInfo]:
    """Collect VLAN subnet information from Meraki networks"""
    subnets = []
    
    try:
        orgs = dashboard.organizations.getOrganizations()
        
        for org in orgs:
            print(f"Processing organization: {org['name']}")
            
            networks = dashboard.organizations.getOrganizationNetworks(org['id'])
            
            for net in networks:
                print(f"  Checking network: {net['name']}")
                
                # Only check networks with appliance
                if 'appliance' not in net.get('productTypes', []):
                    continue
                
                # Get VPN status for all subnets
                vpn_statuses = get_vpn_subnets(dashboard, net['id'])
                
                try:
                    vlans = dashboard.appliance.getNetworkApplianceVlans(net['id'])
                    for vlan in vlans:
                        # Skip excluded VLAN names
                        vlan_name = vlan.get('name', 'Unnamed VLAN')
                        if vlan_name in EXCLUDED_VLAN_NAMES:
                            print(f"    Skipping excluded VLAN name: {vlan_name}")
                            continue
                            
                        if subnet := vlan.get('subnet'):
                            try:
                                network = ipaddress.ip_network(subnet)
                                # Skip if subnet is in excluded ranges
                                if is_excluded_subnet(network):
                                    print(f"    Skipping excluded subnet {subnet} in VLAN {vlan.get('id')}")
                                    continue
                                
                                # Get DHCP lease count for this subnet
                                lease_count = get_dhcp_leases(dashboard, net['id'], network)
                                
                                # Determine VPN status
                                vpn_status = vpn_statuses.get(subnet, "VPN Off")
                                    
                                subnets.append(SubnetInfo(
                                    org_name=org['name'],
                                    net_name=net['name'],
                                    vlan_id=str(vlan.get('id', 'Default')),
                                    subnet=subnet,
                                    vlan_name=vlan_name,
                                    network=network,
                                    dhcp_leases=lease_count,
                                    vpn_status=vpn_status
                                ))
                            except ValueError as e:
                                print(f"    Warning: Invalid subnet {subnet} in VLAN {vlan.get('id')}: {e}")
                
                except meraki.APIError as e:
                    if 'VLANs are not enabled' not in str(e):
                        print(f"    Error checking VLANs for network {net['name']}: {e}")
    
    except meraki.APIError as e:
        print(f"Error accessing Meraki organizations: {e}")
    
    return subnets

def find_overlaps(subnets: List[SubnetInfo]) -> List[OverlapInfo]:
    """Find all overlapping subnets"""
    overlaps = []
    
    for i, subnet1 in enumerate(subnets):
        for subnet2 in subnets[i + 1:]:
            # Skip if subnets are identical and from same network
            if (subnet1.network == subnet2.network and 
                subnet1.org_name == subnet2.org_name and 
                subnet1.net_name == subnet2.net_name):
                continue
                
            # Check for overlap
            if subnet1.network.overlaps(subnet2.network):
                overlaps.append(OverlapInfo(subnet1, subnet2))
    
    return overlaps

def main():
    print("\nCollecting VLAN subnet information from Meraki networks...")
    dashboard = setup_meraki()
    subnets = get_vlan_subnets(dashboard)
    
    if not subnets:
        print("No VLAN subnets found.")
        return
    
    print(f"\nFound {len(subnets)} VLAN subnets. Checking for overlaps...")
    overlaps = find_overlaps(subnets)
    
    if not overlaps:
        print("\nNo subnet overlaps found!")
        return
    
    print(f"\nFound {len(overlaps)} subnet overlaps!")
    
    # Updated headers to include VPN status
    headers = [
        "Network 1", "VLAN 1", "VLAN Name 1", "Subnet 1", "Leases 1", "VPN 1",
        "Network 2", "VLAN 2", "VLAN Name 2", "Subnet 2", "Leases 2", "VPN 2"
    ]
    
    rows = []
    for overlap in overlaps:
        rows.append([
            overlap.subnet1.net_name,
            overlap.subnet1.vlan_id,
            overlap.subnet1.vlan_name,
            overlap.subnet1.subnet,
            overlap.subnet1.dhcp_leases,
            overlap.subnet1.vpn_status,
            overlap.subnet2.net_name,
            overlap.subnet2.vlan_id,
            overlap.subnet2.vlan_name,
            overlap.subnet2.subnet,
            overlap.subnet2.dhcp_leases,
            overlap.subnet2.vpn_status
        ])
    
    # Sort by network names
    rows.sort(key=lambda x: (x[0], x[6]))
    
    print("\nVLAN Subnet Overlap Report")
    print(tabulate(rows, headers=headers, tablefmt="plain"))
    
    # Save report to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"vlan_subnet_overlap_report_{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write("Meraki VLAN Subnet Overlap Report\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(tabulate(rows, headers=headers, tablefmt="plain"))
    
    print(f"\nReport saved to: {report_file}")

if __name__ == "__main__":
    main()
