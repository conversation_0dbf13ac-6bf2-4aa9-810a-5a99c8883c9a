#!/usr/bin/env python3
"""
Script: meraki_device_inventory.py

Generates a complete inventory of all Meraki devices across all organizations and networks, showing:
- Organization Name
- Network Name
- Device Name
- Model
- Serial Number
- MAC Address
- IP Address
- Firmware Version
- Status
- Last Reported At
- Tags
- Notes
- Address/Location

The script can export the inventory to CSV for further analysis.
"""

import csv
from typing import List, NamedTuple
import meraki
from tabulate import tabulate
from datetime import datetime
import os

# Check if running in a terminal that supports colors
COLOR_SUPPORT = os.name == 'posix' and os.isatty(1)

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

class DeviceInfo(NamedTuple):
    org_name: str
    network_name: str
    name: str
    model: str
    serial: str
    mac: str
    ip: str
    firmware: str
    status: str
    last_reported: str
    tags: str
    notes: str
    address: str

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def get_device_inventory(dashboard: meraki.DashboardAPI) -> List[DeviceInfo]:
    """Collect all devices from all networks across all organizations"""
    devices = []

    try:
        # Get all organizations
        orgs = dashboard.organizations.getOrganizations()

        for org in orgs:
            org_id = org['id']
            org_name = org['name']
            print(f"Processing organization: {org_name}")

            try:
                # Get all networks in the organization
                networks = dashboard.organizations.getOrganizationNetworks(org_id)

                # Get all devices in the organization (more efficient than per-network)
                org_devices = dashboard.organizations.getOrganizationDevices(org_id)

                # Create a mapping of network ID to network name
                network_map = {net['id']: net['name'] for net in networks}

                # Process each device
                for device in org_devices:
                    network_name = network_map.get(device.get('networkId', ''), 'Unknown Network')

                    # Get device details
                    try:
                        # Format last reported time
                        last_reported = "N/A"
                        if 'lastReportedAt' in device:
                            try:
                                # Convert UTC timestamp to readable format
                                last_reported_dt = datetime.fromisoformat(device['lastReportedAt'].replace('Z', '+00:00'))
                                last_reported = last_reported_dt.strftime('%Y-%m-%d %H:%M:%S')
                            except (ValueError, TypeError):
                                last_reported = device['lastReportedAt']

                        # Format tags as comma-separated string
                        tags = ", ".join(device.get('tags', []))

                        # Format address
                        address_parts = []
                        if 'address' in device and device['address']:
                            address_parts.append(device['address'])
                        if 'lat' in device and 'lng' in device and device['lat'] and device['lng']:
                            address_parts.append(f"({device['lat']}, {device['lng']})")
                        address = " ".join(address_parts) if address_parts else "N/A"

                        # Determine status based on available information
                        status = device.get('status', '')
                        if status:
                            status = status.capitalize()
                        else:
                            # If no explicit status, try to determine from other fields
                            firmware = device.get('firmware', '')

                            # Check if device has reported recently
                            if 'lastReportedAt' in device and device['lastReportedAt']:
                                try:
                                    # If device has reported in the last 24 hours, consider it online
                                    last_reported_dt = datetime.fromisoformat(device['lastReportedAt'].replace('Z', '+00:00'))
                                    time_diff = datetime.now() - last_reported_dt.replace(tzinfo=None)
                                    if time_diff.total_seconds() < 86400:  # 24 hours in seconds
                                        status = 'Online'
                                    else:
                                        status = 'Offline'
                                except (ValueError, TypeError):
                                    pass

                            # If still no status, use other indicators
                            if not status:
                                if firmware and 'Not running configured version' in firmware:
                                    status = 'Offline'
                                elif device.get('lanIp') or device.get('wan1Ip') or device.get('wan2Ip'):
                                    status = 'Provisioned'
                                else:
                                    status = 'Unknown'

                        devices.append(DeviceInfo(
                            org_name=org_name,
                            network_name=network_name,
                            name=device.get('name', 'Unnamed Device'),
                            model=device.get('model', 'Unknown'),
                            serial=device.get('serial', 'N/A'),
                            mac=device.get('mac', 'N/A'),
                            ip=device.get('lanIp', device.get('wan1Ip', device.get('wan2Ip', 'N/A'))),
                            firmware=device.get('firmware', 'Unknown'),
                            status=status,
                            last_reported=last_reported,
                            tags=tags,
                            notes=device.get('notes', ''),
                            address=address
                        ))
                    except Exception as e:
                        print(f"    Error processing device {device.get('serial', 'Unknown')}: {e}")

            except meraki.APIError as e:
                print(f"  Error getting data for org {org_name}: {e}")

    except meraki.APIError as e:
        print(f"Error accessing Meraki organizations: {e}")

    return devices

def export_to_csv(devices: List[DeviceInfo], filename: str) -> None:
    """Export device inventory to CSV file"""
    with open(filename, 'w', newline='') as csvfile:
        fieldnames = [
            'Organization', 'Network', 'Device Name', 'Model', 'Serial Number',
            'MAC Address', 'IP Address', 'Firmware', 'Status', 'Last Reported',
            'Tags', 'Notes', 'Address'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for device in devices:
            writer.writerow({
                'Organization': device.org_name,
                'Network': device.network_name,
                'Device Name': device.name,
                'Model': device.model,
                'Serial Number': device.serial,
                'MAC Address': device.mac,
                'IP Address': device.ip,
                'Firmware': device.firmware,
                'Status': device.status,
                'Last Reported': device.last_reported,
                'Tags': device.tags,
                'Notes': device.notes,
                'Address': device.address
            })

def main():
    print("\nMeraki Device Inventory Tool")
    print("===========================")
    print("\nConnecting to Meraki dashboard...")
    dashboard = setup_meraki()

    print("Collecting device inventory...")
    devices = get_device_inventory(dashboard)

    if not devices:
        print("\nNo devices found.")
        return

    # Sort devices by organization, network, and name
    devices.sort(key=lambda x: (x.org_name, x.network_name, x.name))

    # Generate report
    headers = ["Organization", "Network", "Device Name", "Model", "Serial Number", "IP Address", "Status", "Firmware"]

    # Add color to status if supported
    rows = []
    for d in devices:
        # Format status with color if terminal supports it
        if COLOR_SUPPORT:
            status = d.status
            if status == "Online":
                status = f"\033[92m{status}\033[0m"  # Green
            elif status == "Offline":
                status = f"\033[91m{status}\033[0m"  # Red
            elif status == "Provisioned":
                status = f"\033[93m{status}\033[0m"  # Yellow

            rows.append([d.org_name, d.network_name, d.name, d.model, d.serial, d.ip, status, d.firmware])
        else:
            rows.append([d.org_name, d.network_name, d.name, d.model, d.serial, d.ip, d.status, d.firmware])

    print("\nMeraki Device Inventory Report")
    print("=" * 100)

    # Calculate column totals
    org_count = len(set(d.org_name for d in devices))
    network_count = len(set(d.network_name for d in devices))
    unique_device_names = len(set(d.name for d in devices))
    model_count = len(set(d.model for d in devices))
    serial_count = len(set(d.serial for d in devices))
    ip_count = sum(1 for d in devices if d.ip and d.ip != 'N/A')
    status_counts = {}
    for d in devices:
        status_counts[d.status] = status_counts.get(d.status, 0) + 1
    firmware_count = len(set(d.firmware for d in devices))

    # Create a totals row with detailed counts
    totals_row = [
        f"Orgs: {org_count}",
        f"Networks: {network_count}",
        f"Names: {unique_device_names}",
        f"Models: {model_count}",
        f"Serials: {serial_count}",
        f"IPs: {ip_count}",
        f"Status: {', '.join(f'{s}={c}' for s, c in status_counts.items())}",
        f"Firmware: {firmware_count}"
    ]

    # Add the totals row to the end
    all_rows = rows.copy()
    all_rows.append(["" for _ in range(len(headers))])
    all_rows.append(totals_row)

    print(tabulate(all_rows, headers=headers, tablefmt="simple"))
    print(f"\nTotal devices found: {len(devices)}")

    # Count devices by model
    model_counts = {}
    for device in devices:
        model_counts[device.model] = model_counts.get(device.model, 0) + 1

    print("\nDevice Count by Model:")
    for model, count in sorted(model_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {model}: {count}")

    # Count devices by organization and model type
    print("\nDevice Count by Organization and Type:")
    org_model_counts = {}
    for device in devices:
        org_name = device.org_name
        # Determine device type from model
        if device.model.startswith('MX'):
            device_type = 'Security Appliance'
        elif device.model.startswith('MS'):
            device_type = 'Switch'
        elif device.model.startswith('MR'):
            device_type = 'Wireless'
        elif device.model.startswith('MG'):
            device_type = 'Cellular Gateway'
        else:
            device_type = 'Other'

        key = (org_name, device_type)
        org_model_counts[key] = org_model_counts.get(key, 0) + 1

    # Display counts by organization and device type
    current_org = None
    for (org, device_type), count in sorted(org_model_counts.items()):
        if org != current_org:
            if current_org is not None:
                print("")
            print(f"  {org}:")
            current_org = org
        print(f"    {device_type}: {count}")

    # Count devices by status
    status_counts = {}
    for device in devices:
        status_counts[device.status] = status_counts.get(device.status, 0) + 1

    print("\nStatus Summary:")
    for status, count in sorted(status_counts.items()):
        print(f"  {status}: {count}")

    # Count devices by network
    print("\nDevice Count by Network:")
    network_counts = {}
    for device in devices:
        network_name = device.network_name
        network_counts[network_name] = network_counts.get(network_name, 0) + 1

    # Display top 10 networks by device count
    print("  Top 10 Networks by Device Count:")
    for network, count in sorted(network_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"    {network}: {count}")

    # Count networks by device count
    network_size_counts = {}
    for count in network_counts.values():
        network_size_counts[count] = network_size_counts.get(count, 0) + 1

    print("\n  Network Size Distribution:")
    for size, count in sorted(network_size_counts.items()):
        print(f"    Networks with {size} device{'s' if size > 1 else ''}: {count}")

    # Export to CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"meraki_inventory_{timestamp}.csv"
    export_to_csv(devices, csv_filename)
    print(f"\nInventory exported to CSV: {csv_filename}")

if __name__ == "__main__":
    main()
