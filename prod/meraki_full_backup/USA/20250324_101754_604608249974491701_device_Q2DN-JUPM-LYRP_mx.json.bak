{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "************", "address": "************/30", "nameservers": {"addresses": ["*******", "*******"]}}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": {"perfScore": 71.5}, "vlans": [{"id": 1, "networkId": "N_604608249974507864", "name": "Data", "applianceIp": "***********", "subnet": "***********/25", "groupPolicyId": "102", "fixedIpAssignments": {"00:1a:a0:dd:08:8e": {"ip": "***********6", "name": null}, "00:1a:a0:dd:b6:a9": {"ip": "***********9", "name": null}, "50:65:f3:21:31:7d": {"ip": "************", "name": null}, "50:65:f3:25:2a:a0": {"ip": "************", "name": "Shared Drive"}, "50:65:f3:28:dc:70": {"ip": "************", "name": "Shared Drive"}, "e4:11:5b:56:d2:cf": {"ip": "************", "name": null}}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249974503606"}, {"id": 2, "networkId": "N_604608249974507864", "name": "Voice", "applianceIp": "***********29", "subnet": "***********28/26", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "***********81", "end": "***********90", "comment": "Reserved"}], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249974503608"}, {"id": 20, "networkId": "N_604608249974507864", "name": "P2P", "applianceIp": "*************", "subnet": "*************/30", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "upstream_dns", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974503610"}, {"id": 70, "networkId": "N_604608249974507864", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974593362"}, {"id": 75, "networkId": "N_604608249974507864", "name": "Guest Wifi", "applianceIp": "************29", "subnet": "************28/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974593363"}, {"id": 85, "networkId": "N_604608249974507864", "name": "City of Rochester", "applianceIp": "**************", "subnet": "************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "upstream_dns", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974503733"}, {"id": 400, "networkId": "N_604608249974507864", "name": "MGMT", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "upstream_dns", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974587533"}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,************/32,**************/32,***********/32,***********/32,***************/32,**************/32,*************/32,************/23,*************/32,*************/32", "syslogEnabled": false}, {"comment": "City of Rochester", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********/25", "destPort": "80,443,7563,2638,3110", "destCidr": "************/24", "syslogEnabled": false}, {"comment": "City of Rochester", "policy": "allow", "protocol": "udp", "srcPort": "Any", "srcCidr": "***********/25", "destPort": "2638", "destCidr": "************/24", "syslogEnabled": false}, {"comment": "permit to vendor app", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********/25", "destPort": "1433", "destCidr": "192.168.85.55/32", "syslogEnabled": false}, {"comment": "permit to vendor app", "policy": "allow", "protocol": "udp", "srcPort": "Any", "srcCidr": "***********/25", "destPort": "1433", "destCidr": "192.168.85.55/32", "syslogEnabled": false}, {"comment": "", "policy": "allow", "protocol": "icmp", "srcPort": "Any", "srcCidr": "***********/25", "destPort": "Any", "destCidr": "************/24", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,**********/12,***********/16", "destPort": "Any", "destCidr": "***********/16", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/16", "destPort": "Any", "destCidr": "10.0.0.0/8,**********/12,***********/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": [{"id": "981f3db1-30a4-4122-be09-81258c30836a", "networkId": "N_604608249974507864", "enabled": true, "name": "CBTS Management", "subnet": "*************/32", "gatewayIp": "*************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}]}