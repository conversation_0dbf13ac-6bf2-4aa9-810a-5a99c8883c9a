[{"id": 1, "networkId": "N_604608249974525169", "name": "<PERSON><PERSON><PERSON>", "applianceIp": "***********", "subnet": "***********/25", "fixedIpAssignments": {"00:50:c2:56:d8:db": {"ip": "***********", "name": "fuel system"}}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "12 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249974532495", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "N_604608249974525169", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974553232", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "N_604608249974525169", "name": "Guest Wifi", "applianceIp": "************29", "subnet": "************28/25", "groupPolicyId": "102", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974609333", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 82, "networkId": "N_604608249974525169", "name": "FSTU", "applianceIp": "************", "subnet": "************/23", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974609243", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "N_604608249974525169", "name": "MGMT", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974587418", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}]