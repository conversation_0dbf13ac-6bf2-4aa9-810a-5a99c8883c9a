{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "*************", "address": "*************/30", "nameservers": {"addresses": ["*******", "*******"]}}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": {"perfScore": 17.0}, "vlans": [{"id": 10, "networkId": "L_604608249974505289", "name": "Data", "applianceIp": "***********", "subnet": "***********/25", "groupPolicyId": "102", "fixedIpAssignments": {"74:bf:c0:e0:46:1f": {"ip": "************"}, "f8:a2:6d:e0:88:3b": {"ip": "************"}, "f8:a2:6d:e0:88:95": {"ip": "************"}}, "reservedIpRanges": [], "dnsNameservers": "**********\n10.40.252.11\n10.40.252.12\n*******\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249975186199", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 33, "networkId": "L_604608249974505289", "name": "Access services", "applianceIp": "************", "subnet": "************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249975194248", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "L_604608249974505289", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249975186198", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "L_604608249974505289", "name": "Guest Wifi", "applianceIp": "************29", "subnet": "************28/25", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249975186201", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "L_604608249974505289", "name": "Management", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "*************\n10.158.130.50\n*******", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249975186202", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,************/32,**************/32,***********/32,***********/32,***************/32,**************/32,*************/32,************/23,*************/32,*************/32", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/16", "destPort": "Any", "destCidr": "10.0.0.0/8,**********/12,***********/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": [{"id": "2f15363c-7ec1-4812-9b93-7c0e9abf7af4", "networkId": "L_604608249974505289", "enabled": true, "name": "Access services", "subnet": "************/32", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "00154bad-0614-447a-afdc-838f3302e971", "networkId": "L_604608249974505289", "enabled": true, "name": "Access services", "subnet": "************/32", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "71233296-e519-4c09-a4ee-70af2088a39a", "networkId": "L_604608249974505289", "enabled": true, "name": "Access Services", "subnet": "************/32", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}]}