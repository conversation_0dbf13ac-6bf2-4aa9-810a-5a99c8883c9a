[{"number": 0, "name": "Corp-WiFi", "enabled": true, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "8021x-radius", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa-eap", "wpaEncryptionMode": "WPA2 only", "radiusServers": [{"host": "*************", "port": 1812, "id": "604608249974616810", "radsecEnabled": false, "openRoamingCertificateId": null, "caCertificate": null}, {"host": "***********", "port": 1812, "id": "604608249974616812", "radsecEnabled": false, "openRoamingCertificateId": null, "caCertificate": null}], "radiusAccountingEnabled": false, "radiusTestingEnabled": true, "radiusServerTimeout": 1, "radiusServerAttemptsLimit": 3, "radiusFallbackEnabled": false, "radiusProxyEnabled": false, "radiusCoaEnabled": false, "radiusCalledStationId": "$NODE_MAC$:$VAP_NAME$", "radiusAuthenticationNasId": "$NODE_MAC$:$VAP_NUM$", "radiusAttributeForGroupPolicies": "Filter-Id", "ipAssignmentMode": "Bridge mode", "useVlanTagging": false, "radiusOverride": false, "minBitrate": 6, "bandSelection": "Dual band operation with Band Steering", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_Enterprise", "ssid_enterprise"], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 1, "name": "Fleet", "enabled": true, "splashPage": "Click-through splash page", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA3 Transition Mode", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 70, "adminSplashUrl": "", "splashTimeout": "129600 minutes", "walledGardenEnabled": false, "minBitrate": 6, "bandSelection": "Dual band operation with Band Steering", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_Fleet", "ssid_fleet"], "speedBurst": {"enabled": false}}, {"number": 2, "name": "Guest", "enabled": true, "splashPage": "Click-through splash page", "ssidAdminAccessible": false, "authMode": "open", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 75, "adminSplashUrl": "", "splashTimeout": "1440 minutes", "walledGardenEnabled": false, "minBitrate": 6, "bandSelection": "Dual band operation with Band Steering", "perClientBandwidthLimitUp": 500, "perClientBandwidthLimitDown": 500, "perSsidBandwidthLimitUp": 1024, "perSsidBandwidthLimitDown": 1024, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_Guest", "ssid_guest"], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 3, "name": "55431", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA1 and WPA2", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 80, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["ssid_55431"], "speedBurst": {"enabled": false}}, {"number": 4, "name": "EXT-IOT", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA3 Transition Mode", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 70, "minBitrate": 12, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_EXT-IOT"], "speedBurst": {"enabled": false}}, {"number": 5, "name": "TD-WiFi", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA3 Transition Mode", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 70, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": false, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}}, {"number": 6, "name": "A-Mon", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 105, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["ssid_amon"], "speedBurst": {"enabled": false}}, {"number": 7, "name": "TDC", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "8021x-radius", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa-eap", "wpaEncryptionMode": "WPA2 only", "radiusServers": [{"host": "***********", "port": 1812, "id": "604608249974623005", "radsecEnabled": false, "openRoamingCertificateId": null, "caCertificate": null}], "radiusAccountingEnabled": false, "radiusTestingEnabled": true, "radiusServerTimeout": 1, "radiusServerAttemptsLimit": 3, "radiusFallbackEnabled": false, "radiusProxyEnabled": false, "radiusCoaEnabled": false, "radiusCalledStationId": "$NODE_MAC$:$VAP_NAME$", "radiusAuthenticationNasId": "$NODE_MAC$:$VAP_NUM$", "radiusAttributeForGroupPolicies": "Filter-Id", "ipAssignmentMode": "Bridge mode", "useVlanTagging": false, "radiusOverride": false, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_TDC"], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 8, "name": "TDCMigrate", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": false, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}}, {"number": 9, "name": "TDC_GUEST", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 75, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 500, "perClientBandwidthLimitDown": 500, "perSsidBandwidthLimitUp": 1024, "perSsidBandwidthLimitDown": 1024, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["TDC_GUEST"], "speedBurst": {"enabled": false}}, {"number": 10, "name": "TDVendor", "enabled": true, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 80, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["TDVendor"], "speedBurst": {"enabled": false}}, {"number": 11, "name": "Unconfigured SSID 12", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "open", "ipAssignmentMode": "NAT mode", "adultContentFilteringEnabled": false, "dnsRewrite": {"enabled": false, "dnsCustomNameservers": []}, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "visible": true, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 12, "name": "Unconfigured SSID 13", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "open", "ipAssignmentMode": "NAT mode", "adultContentFilteringEnabled": false, "dnsRewrite": {"enabled": false, "dnsCustomNameservers": []}, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_TDC_GUEST"], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 13, "name": "Unconfigured SSID 14", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "open", "ipAssignmentMode": "NAT mode", "adultContentFilteringEnabled": false, "dnsRewrite": {"enabled": false, "dnsCustomNameservers": []}, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "visible": true, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 14, "name": "Unconfigured SSID 15", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "open", "ipAssignmentMode": "NAT mode", "adultContentFilteringEnabled": false, "dnsRewrite": {"enabled": false, "dnsCustomNameservers": []}, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "visible": true, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}, "psk": "*************"}]