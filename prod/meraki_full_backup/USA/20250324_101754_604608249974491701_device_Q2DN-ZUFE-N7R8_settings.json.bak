{"management": {"wan1": {"wanEnabled": "not configured", "usingStaticIp": false, "vlan": null}, "wan2": {"wanEnabled": "enabled", "usingStaticIp": false, "vlan": null}, "ddnsHostnames": {"activeDdnsHostname": "mx50000aa01-rtmkmtzjqh.dynamic-m.com", "ddnsHostnameWan1": "mx50000aa01-rtmkmtzjqh-1.dynamic-m.com", "ddnsHostnameWan2": "mx50000aa01-rtmkmtzjqh-2.dynamic-m.com"}}, "status": [{"name": "MX55835CT01", "serial": "Q2DN-ZUFE-N7R8", "mac": "88:15:44:6a:78:b0", "publicIp": "**************", "networkId": "L_604608249974505643", "status": "online", "lastReportedAt": "2025-03-24T14:25:29.417000Z", "productType": "appliance", "components": {"powerSupplies": []}, "model": "MX80", "tags": ["recently-added"], "usingCellularFailover": false, "wan1Ip": "**************", "wan1Gateway": "************", "wan1IpType": "dhcp", "wan1PrimaryDns": "***********", "wan1SecondaryDns": "***********", "wan2Ip": "************", "wan2Gateway": "************", "wan2IpType": "dhcp", "wan2PrimaryDns": "************", "wan2SecondaryDns": null}], "uplink": null}