{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "************", "address": "************/28", "nameservers": {"addresses": ["*******", "*******"]}}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": null, "vlans": null, "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,*************/32", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,**********/12,***********/16", "destPort": "Any", "destCidr": "***********/16", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/16", "destPort": "Any", "destCidr": "10.0.0.0/8,**********/12,***********/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": null}