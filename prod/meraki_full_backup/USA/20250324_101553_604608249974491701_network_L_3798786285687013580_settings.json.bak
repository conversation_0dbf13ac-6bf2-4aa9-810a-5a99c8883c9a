{"alerts": {"defaultDestinations": {"emails": [], "snmp": false, "allAdmins": true, "httpServerIds": []}, "alerts": [{"type": "ampMalwareDetected", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": true, "httpServerIds": []}, "filters": {}}, {"type": "ampMalwareBlocked", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "applianceDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60}}, {"type": "failoverEvent", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcpNoLeases", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "rogueDhcp", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "ipConflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "ip6Conflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcp6naRenumber", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcp6pdRenumber", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "cellularUpDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "clientConnectivity", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"clients": []}}, {"type": "vrrp", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "vpnConnectivityChange", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "settingsChanged", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"threshold": 104857600, "period": 1200}}, {"type": "weeklyUmbrella", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "prefixStarvation", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "portDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60, "selector": "any port"}}, {"type": "powerSupplyDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "rpsBackup", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "udldError", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "portError", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"selector": "any port"}}, {"type": "portSpeed", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"selector": "any port"}}, {"type": "newDhcpServer", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "switchDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60}}, {"type": "switchCriticalTemperature", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}], "muting": {}}, "snmp": {"access": "community", "communityString": "gls1stgrp", "users": []}, "syslog": {"servers": [{"host": "**********", "port": "514", "transportProtocol": "UDP", "encryption": {"enabled": false, "certificate": {"id": ""}}, "roles": ["Flows", "URLs", "Security events", "Appliance event log"]}]}, "firmware": {"products": {"appliance": {"currentVersion": {"id": "3089", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2024-06-05T23:12:07Z", "shortName": "MX 18.211.2"}, "lastUpgrade": {"time": "2024-06-27T02:30:39Z", "fromVersion": {"id": "2283", "firmware": "wired-17-6", "releaseType": "legacy", "releaseDate": "2022-03-17T21:34:58Z", "shortName": "MX 17.6"}, "toVersion": {"id": "3089", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2024-06-05T23:12:07Z", "shortName": "MX 18.211.2"}}, "nextUpgrade": {"time": "", "toVersion": {}}, "availableVersions": [{"id": "4625", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2025-03-10T19:27:18Z", "shortName": "MX **********"}, {"id": "4727", "firmware": "wired-19-1-7", "releaseType": "candidate", "releaseDate": "2025-03-19T07:25:34Z", "shortName": "MX ********"}], "participateInNextBetaRelease": false}, "switch": {"currentVersion": {"id": "2871", "firmware": "switch-16-8", "releaseType": "legacy", "releaseDate": "2024-04-22T21:38:57Z", "shortName": "MS 16.8"}, "lastUpgrade": {"time": "", "fromVersion": {}, "toVersion": {}}, "nextUpgrade": {"time": "", "toVersion": {}}, "availableVersions": [{"id": "4633", "firmware": "switch-17-2-1", "releaseType": "stable", "releaseDate": "2025-03-19T23:58:12Z", "shortName": "MS 17.2.1"}], "participateInNextBetaRelease": false}, "switchCatalyst": {"currentVersion": {"id": "2928", "firmware": "cs-16-8", "releaseType": "legacy", "releaseDate": "2024-04-08T16:41:15Z", "shortName": "CS 16.8"}, "lastUpgrade": {"time": "", "fromVersion": {}, "toVersion": {}}, "nextUpgrade": {"time": "", "toVersion": {}, "issu": false}, "availableVersions": [{"id": "3896", "firmware": "cs-17-1-4", "releaseType": "stable", "releaseDate": "2024-11-15T20:36:44Z", "shortName": "CS 17.1.4"}, {"id": "4429", "firmware": "cs-iosxe-17-15-2", "releaseType": "beta", "releaseDate": "2025-02-06T21:14:45Z", "shortName": "IOS XE 17.15.2"}], "participateInNextBetaRelease": false}}, "timezone": "US/Central", "upgradeWindow": {"dayOfWeek": "Wed", "hourOfDay": "1:00"}}}