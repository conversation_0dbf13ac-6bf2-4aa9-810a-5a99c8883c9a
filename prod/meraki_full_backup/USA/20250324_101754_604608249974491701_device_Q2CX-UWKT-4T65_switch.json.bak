{"ports": [{"portId": "1", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "2", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "3", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "4", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "5", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "6", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "7", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "8", "name": null, "tags": [], "enabled": true, "poeEnabled": true, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)", "100 Megabit (auto)", "100 Megabit half duplex (forced)", "100 Megabit full duplex (forced)", "10 Megabit (auto)", "10 Megabit half duplex (forced)", "10 Megabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "9", "name": null, "tags": [], "enabled": true, "poeEnabled": false, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}, {"portId": "10", "name": null, "tags": [], "enabled": true, "poeEnabled": false, "type": "trunk", "vlan": 1, "voiceVlan": null, "allowedVlans": "all", "isolationEnabled": false, "rstpEnabled": true, "stpGuard": "disabled", "linkNegotiation": "Auto negotiate", "portScheduleId": null, "schedule": null, "udld": "<PERSON><PERSON> only", "linkNegotiationCapabilities": ["Auto negotiate", "1 Gigabit full duplex (forced)"], "accessPolicyType": "Open", "daiTrusted": false, "profile": {"enabled": false, "id": "", "iname": null}, "module": {"model": null}, "mirror": {"mode": "Not mirroring traffic"}}], "routing": [], "warm_spare": {"enabled": false}}