[{"id": 2, "networkId": "N_604608249974615381", "name": "Voice", "applianceIp": "**************", "subnet": "**************/26", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "**************", "end": "**************", "comment": "Reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "hex", "code": "125", "value": "00:00:04:03:6C:69:64:3A:69:70:70:68:6F:6E:65:2E:6D:69:74:65:6C:2E:63:6F:6D:3B:73:77:5F:74:66:74:70:3D:31:30:2E:31:33:36:2E:32:30:33:2E:32:35:31:3B:63:61:6C:6C:5F:73:72:76:3D:31:30:2E:31:33:36:2E:32:30:33:2E:32:35:31:2C:20:31:30:2E:31:33:36:2E:32:30:35:2E:31:39:30:3B:64:73:63:70:3D:34:36:3B"}], "interfaceId": "604608249977319578", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 10, "networkId": "N_604608249974615381", "name": "Data", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n10.40.64.5\n10.40.64.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249977319579", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "N_604608249974615381", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249977319580", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "N_604608249974615381", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249977319581", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 105, "networkId": "N_604608249974615381", "name": "DriveCam", "applianceIp": "*************", "subnet": "*************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249977319577", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "N_604608249974615381", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249977319582", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}]