[{"id": 2, "networkId": "N_604608249974583534", "name": "Voice", "applianceIp": "**************", "subnet": "**************/26", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974594159", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 10, "networkId": "N_604608249974583534", "name": "Data", "applianceIp": "************", "subnet": "************/25", "groupPolicyId": "102", "fixedIpAssignments": {"34:9f:7b:58:bc:ca": {"ip": "*************"}, "34:9f:7b:d2:b0:bf": {"ip": "*************"}}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n8.8.8.8\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974594156", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 40, "networkId": "N_604608249974583534", "name": "Surveillance", "applianceIp": "************", "subnet": "************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249975740589", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "N_604608249974583534", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974594158", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "N_604608249974583534", "name": "Guest Wifi", "applianceIp": "************29", "subnet": "**************/25", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974594160", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "N_604608249974583534", "name": "Management", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974594161", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}]