{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": null, "vlans": [{"id": 2, "networkId": "N_604608249974534654", "name": "Voice", "applianceIp": "**************", "subnet": "**************/26", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "**************", "end": "**************", "comment": "Reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "hex", "code": "125", "value": "00:00:04:03:6C:69:64:3A:69:70:70:68:6F:6E:65:2E:6D:69:74:65:6C:2E:63:6F:6D:3B:73:77:5F:74:66:74:70:3D:31:30:2E:31:33:36:2E:32:30:33:2E:32:35:31:3B:63:61:6C:6C:5F:73:72:76:3D:31:30:2E:31:33:36:2E:32:30:33:2E:32:35:31:2C:20:31:30:2E:31:33:36:2E:32:30:35:2E:31:39:30:3B:64:73:63:70:3D:34:36:3B"}], "interfaceId": "604608249974602400", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 10, "networkId": "N_604608249974534654", "name": "<PERSON><PERSON><PERSON>", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.64.5\n10.40.64.4\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974542971", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "N_604608249974534654", "name": "Fleet Wifi", "applianceIp": "**********", "subnet": "**********/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974542974", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "N_604608249974534654", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974543065", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 105, "networkId": "N_604608249974534654", "name": "DriveCam", "applianceIp": "*************", "subnet": "*************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249977130648", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "N_604608249974534654", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974554240", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,*************/32", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,***********/16", "destPort": "Any", "destCidr": "***********/16", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/16", "destPort": "Any", "destCidr": "10.0.0.0/8,***********/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": [{"id": "fa989820-bf4f-4b0d-81dc-ffd27766fd19", "networkId": "N_604608249974534654", "enabled": true, "name": "Cisco Router", "subnet": "************/32", "gatewayIp": "*************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "661d8f66-f132-4dce-8649-79426d382e79", "networkId": "N_604608249974534654", "enabled": false, "name": "Cologix MPLS", "subnet": "**************/28", "gatewayIp": "**************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "e8cb4be7-b2c7-4669-8889-c4491a8a8d07", "networkId": "N_604608249974534654", "enabled": false, "name": "38702 Voice", "subnet": "************/24", "gatewayIp": "**************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "e16463fc-72f1-4355-94b6-c7e8f7089bdd", "networkId": "N_604608249974534654", "enabled": false, "name": "38702C Voice", "subnet": "**************/26", "gatewayIp": "**************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}]}