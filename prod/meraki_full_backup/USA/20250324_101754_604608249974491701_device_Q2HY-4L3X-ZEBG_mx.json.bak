{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": {"perfScore": 10.0}, "vlans": [{"id": 1, "networkId": "L_604608249974506396", "name": "<PERSON><PERSON><PERSON>", "applianceIp": "************", "subnet": "************/25", "groupPolicyId": "102", "fixedIpAssignments": {"d8:49:2f:58:bf:0e": {"ip": "*************"}, "f8:0d:60:9a:ce:72": {"ip": "************"}}, "reservedIpRanges": [{"start": "************", "end": "************1", "comment": "Reserved"}, {"start": "************00", "end": "************27", "comment": "Reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n8.8.8.8\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249976003664", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 2, "networkId": "L_604608249974506396", "name": "Voice", "applianceIp": "**************", "subnet": "**************/26", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249976068832", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "L_604608249974506396", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249976003665", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "L_604608249974506396", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249976003666", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 80, "networkId": "L_604608249974506396", "name": "TDVendor", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27\n8.8.8.8", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249976369728", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 85, "networkId": "L_604608249974506396", "name": "DMZ", "applianceIp": "***********", "subnet": "***********/30", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n8.8.8.8\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "3798786285687099090", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "L_604608249974506396", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27\n8.8.8.8", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249976003667", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}], "firewall": {"rules": [{"comment": "VLAN1 To DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "************/25", "destPort": "135,139,445,443,3389", "destCidr": "***********/32", "syslogEnabled": false}, {"comment": "DMZ host to DNS", "policy": "allow", "protocol": "udp", "srcPort": "Any", "srcCidr": "***********/32", "destPort": "53", "destCidr": "GRP(3798786285687013384)", "syslogEnabled": false}, {"comment": "DMZ PING", "policy": "allow", "protocol": "icmp", "srcPort": "Any", "srcCidr": "***********/30", "destPort": "Any", "destCidr": "GRP(3798786285687013384)", "syslogEnabled": false}, {"comment": "DMZ host Web", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********/32", "destPort": "443,80", "destCidr": "Any", "syslogEnabled": false}, {"comment": "DMZ host to WSUS/SCCM", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********/32", "destPort": "Any", "destCidr": "GRP(3798786285687013383)", "syslogEnabled": false}, {"comment": "DMZ to Hosts", "policy": "allow", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/30", "destPort": "Any", "destCidr": "*************/32,*************/32", "syslogEnabled": false}, {"comment": "DMZ host to LAN", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/32", "destPort": "Any", "destCidr": "************/25,**********/12,***********/16", "syslogEnabled": false}, {"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,************/32,**************/32,***********/32,***********/32,***************/32,**************/32,*************/32,************/23,*************/32,*************/32", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,**********/12,***********/16", "destPort": "Any", "destCidr": "***********/16", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/16", "destPort": "Any", "destCidr": "10.0.0.0/8,**********/12,***********/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": []}