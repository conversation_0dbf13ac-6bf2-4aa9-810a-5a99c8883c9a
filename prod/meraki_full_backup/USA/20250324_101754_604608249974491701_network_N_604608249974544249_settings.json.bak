{"alerts": {"defaultDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "alerts": [{"type": "gatewayDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60}}, {"type": "gatewayToRepeater", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "repeaterDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60}}, {"type": "rogueAp", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "settingsChanged", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "snr", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"selector": "{}"}}, {"type": "highWirelessUsage", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"selector": "{}"}}, {"type": "onboarding", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"selector": "{\"smartSensitivity\":\"medium\",\"smartEnabled\":false,\"eventReminderPeriodSecs\":10800}"}}, {"type": "vpnConnectivityChange", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"threshold": 104857600, "period": 1200}}, {"type": "weeklyPresence", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "uplinkIp6Conflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}], "muting": {"byPortSchedules": {"enabled": false}}}, "snmp": {"access": "none", "communityString": null, "users": []}, "syslog": {"servers": [{"host": "**********", "port": "514", "transportProtocol": "UDP", "encryption": {"enabled": false, "certificate": {"id": ""}}, "roles": ["Air Marshal events", "Flows", "URLs", "Wireless event log"]}]}, "firmware": {"products": {"wireless": {"currentVersion": {"id": "2787", "firmware": "wireless-30-6", "releaseType": "legacy", "releaseDate": "2024-08-15T21:21:40Z", "shortName": "MR 30.6"}, "lastUpgrade": {"time": "2024-05-19T03:07:57Z", "fromVersion": {"id": "2510", "firmware": "wireless-29-6-1", "releaseType": "stable", "releaseDate": "2023-05-25T23:23:29Z", "shortName": "MR 29.6.1"}, "toVersion": {"id": "2787", "firmware": "wireless-30-6", "releaseType": "legacy", "releaseDate": "2024-08-15T21:21:40Z", "shortName": "MR 30.6"}}, "nextUpgrade": {"time": "", "toVersion": {}}, "availableVersions": [{"id": "4542", "firmware": "wireless-31-1-6", "releaseType": "stable", "releaseDate": "2025-03-05T16:09:12Z", "shortName": "MR 31.1.6"}], "participateInNextBetaRelease": false}}, "timezone": "America/Los_Angeles", "upgradeWindow": {"dayOfWeek": "<PERSON><PERSON>", "hourOfDay": "4:00"}}}