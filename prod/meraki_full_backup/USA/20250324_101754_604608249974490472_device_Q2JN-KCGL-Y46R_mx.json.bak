{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "*************", "address": "*************/30", "nameservers": {"addresses": ["*******", "*************"]}}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}, "ipv6": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": null, "vlans": [{"id": 2, "networkId": "N_604608249974598170", "name": "Voice", "applianceIp": "************", "subnet": "************/24", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "************", "end": "************1", "comment": "Reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "hex", "code": "125", "value": "00:00:04:03:6C:69:64:3A:69:70:70:68:6F:6E:65:2E:6D:69:74:65:6C:2E:63:6F:6D:3B:73:77:5F:74:66:74:70:3D:31:30:2E:31:33:36:2E:32:30:33:2E:32:35:31:3B:63:61:6C:6C:5F:73:72:76:3D:31:30:2E:31:33:36:2E:32:30:33:2E:32:35:31:2C:20:31:30:2E:31:33:36:2E:32:30:35:2E:31:39:30:3B:64:73:63:70:3D:34:36:3B"}], "interfaceId": "604608249974699407", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 10, "networkId": "N_604608249974598170", "name": "Data", "applianceIp": "************", "subnet": "************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n10.40.64.5\n10.40.64.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974699402", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "N_604608249974598170", "name": "Fleet Wifi", "applianceIp": "**********", "subnet": "**********/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "*******\n1.0.0.1", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974699404", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "N_604608249974598170", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974699405", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 101, "networkId": "N_604608249974598170", "name": "Radio", "applianceIp": "*************", "subnet": "***********/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974699410", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "N_604608249974598170", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974699406", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,*************/32", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Radio", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "************/24", "destPort": "5555", "destCidr": "***********/24", "syslogEnabled": true}, {"comment": "Radio", "policy": "allow", "protocol": "udp", "srcPort": "Any", "srcCidr": "************/24", "destPort": "5555-5656", "destCidr": "***********/24", "syslogEnabled": true}, {"comment": "Radio", "policy": "allow", "protocol": "icmp", "srcPort": "Any", "srcCidr": "************/24", "destPort": "Any", "destCidr": "***********/24", "syslogEnabled": true}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,***********/16", "destPort": "Any", "destCidr": "***********/16", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/16", "destPort": "Any", "destCidr": "10.0.0.0/8,***********/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": [{"id": "f24a0935-64d6-4ed0-a3fb-a322d1edb5af", "networkId": "N_604608249974598170", "enabled": false, "name": "SOFA-VDC VPN", "subnet": "*************/32", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "018d9e30-6772-42d6-9764-7cd3f09c9ee0", "networkId": "N_604608249974598170", "enabled": true, "name": "Cisco Router", "subnet": "************/32", "gatewayIp": "*************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "56ffdcce-ef19-4666-a07f-5dba6e655479", "networkId": "N_604608249974598170", "enabled": false, "name": "MPLS 2", "subnet": "**********/24", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "afa5e492-6dc2-4fe4-993c-7ec980bc4e10", "networkId": "N_604608249974598170", "enabled": false, "name": "MPLS 3", "subnet": "**********/24", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "b89405ce-25cc-4274-930b-0193de4ce27c", "networkId": "N_604608249974598170", "enabled": false, "name": "Cologix Voice", "subnet": "**************/28", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "38378544-cb5d-4a20-9325-92ec3bfdca26", "networkId": "N_604608249974598170", "enabled": false, "name": "38702B Voice", "subnet": "**************/26", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}, {"id": "7d2e8412-4c1f-49df-8859-ed48d5f53efc", "networkId": "N_604608249974598170", "enabled": false, "name": "38702C Voice", "subnet": "**************/26", "gatewayIp": "************", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}]}