{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "*************", "address": "*************/27", "nameservers": {"addresses": ["*******", "*******"]}}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "************", "address": "************/28", "nameservers": {"addresses": ["*******", "*******"]}}}, "pppoe": {"enabled": false}}}}, "performance": null, "vlans": [{"id": 1, "networkId": "N_604608249974591272", "name": "Data", "applianceIp": "**********", "subnet": "**********/25", "groupPolicyId": "104", "fixedIpAssignments": {"84:69:93:0a:ae:7d": {"ip": "***********", "name": null}}, "reservedIpRanges": [{"start": "**********", "end": "***********", "comment": "Static Reservation"}, {"start": "**********00", "end": "**********06", "comment": "Radios"}, {"start": "**********07", "end": "**********17", "comment": "Reserved"}, {"start": "**********18", "end": "**********20", "comment": "Access Points"}, {"start": "**********21", "end": "**********27", "comment": "End"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "12 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974602211"}, {"id": 2, "networkId": "N_604608249974591272", "name": "Voice", "applianceIp": "**********29", "subnet": "**********28/26", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "**********29", "end": "**********38", "comment": "Static Reservation"}, {"start": "**********35", "end": "**********35", "comment": "Fuel System"}, {"start": "**********36", "end": "**********36", "comment": "Fuel System"}, {"start": "**********70", "end": "**********70", "comment": "Key System"}], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "12 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249974602213"}, {"id": 20, "networkId": "N_604608249974591272", "name": "p2p", "applianceIp": "************", "subnet": "************/30", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974602212"}, {"id": 70, "networkId": "N_604608249974591272", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974602216"}, {"id": 75, "networkId": "N_604608249974591272", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974602215"}, {"id": 101, "networkId": "N_604608249974591272", "name": "Data 2", "applianceIp": "***********", "subnet": "***********/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974602217"}, {"id": 400, "networkId": "N_604608249974591272", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974602218"}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,************/32,**************/32,***********/32,***********/32,***************/32,**************/32,*************/32,************/23,*************/32,*************/32", "syslogEnabled": false}, {"comment": "Block **************", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32", "syslogEnabled": false}, {"comment": "Permit RFC 1918", "policy": "allow", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,**********/12", "destPort": "Any", "destCidr": "10.0.0.0/8,**********/12", "syslogEnabled": false}, {"comment": "Block TCP 135, 137,138,139, 445", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445", "destCidr": "Any", "syslogEnabled": true}, {"comment": "Block UDP 69,135, 137,138,139,161,162,514", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": true}, {"comment": "Block TCP 6660-6669", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "6660-6669", "destCidr": "Any", "syslogEnabled": true}, {"comment": "Allow DMZ to DB Server", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********15/32", "destPort": "1433", "destCidr": "10.136.0.34/32", "syslogEnabled": true}, {"comment": "Allow DB Server to DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "10.136.0.34/32", "destPort": "1433", "destCidr": "***********02/32,***********00/32,***********15/32", "syslogEnabled": true}, {"comment": "server to DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********10/32", "destPort": "1433", "destCidr": "***********/32", "syslogEnabled": true}, {"comment": "DMZ to server", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********/32", "destPort": "1433", "destCidr": "***********10/32", "syslogEnabled": true}, {"comment": "Allow DMZ to Internal App", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********15/32", "destPort": "11005,11017,11018", "destCidr": "***********/32", "syslogEnabled": true}, {"comment": "Allow DMZ to internal APP", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********10/32", "destPort": "11005,11017,11018", "destCidr": "************/32", "syslogEnabled": true}, {"comment": "Allow Internal App to DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********/32", "destPort": "11005,11017,11018", "destCidr": "***********02/32,***********00/32,***********15/32", "syslogEnabled": true}, {"comment": "Allow Internal App to DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "************/32", "destPort": "11005,11017,11018", "destCidr": "***********10/32", "syslogEnabled": true}, {"comment": "Allow Corp to RDP DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "**********/25", "destPort": "3389", "destCidr": "***********02/32,***********00/32,***********15/32", "syslogEnabled": true}, {"comment": "Allow Corp to Xgate", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "10.0.0.0/8,**********/12", "destPort": "2001,3389", "destCidr": "***********01/32", "syslogEnabled": true}, {"comment": "Allow Xgate SQL to Local", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********01/32", "destPort": "1433", "destCidr": "10.136.0.34/32", "syslogEnabled": true}, {"comment": "Allow Internal App to DMZ", "policy": "allow", "protocol": "udp", "srcPort": "Any", "srcCidr": "***********/32", "destPort": "11005,11017,11018", "destCidr": "***********02/32,***********00/32,***********15/32", "syslogEnabled": true}, {"comment": "Allow PASS-App to local", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********02/32", "destPort": "1433", "destCidr": "***********/32", "syslogEnabled": true}, {"comment": "Allow Internal App to DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "************/32", "destPort": "11005,11017,11018", "destCidr": "***********10/32", "syslogEnabled": true}, {"comment": "Allow DMZ to Internal App", "policy": "allow", "protocol": "udp", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********15/32", "destPort": "11005,11017,11018,8080", "destCidr": "***********/32", "syslogEnabled": true}, {"comment": "Allow DMZ to Internal App", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********10/32", "destPort": "11005,11017,11018,8080", "destCidr": "************/32", "syslogEnabled": true}, {"comment": "Allow hosts to DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********/32,***********/32", "destPort": "9000", "destCidr": "***********02/32,***********00/32,***********15/32", "syslogEnabled": true}, {"comment": "Allow hosts to DMZ", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "************/32,***********/32", "destPort": "9000", "destCidr": "***********10/32", "syslogEnabled": true}, {"comment": "Allow DMZ to host", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********15/32", "destPort": "8080,11007,1433", "destCidr": "***********/32", "syslogEnabled": true}, {"comment": "Allow DMZ to host", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********15/32", "destPort": "11005,11017,11018,8080,9000", "destCidr": "***********/32", "syslogEnabled": true}, {"comment": "Allow DMZ to host", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********10/32", "destPort": "11005,11017,11018,8080,9000", "destCidr": "************/32", "syslogEnabled": true}, {"comment": "Deny VDS to Corp", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "************/27", "destPort": "Any", "destCidr": "**********/25", "syslogEnabled": false}, {"comment": "Deny Corp to VDS", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "**********/25", "destPort": "Any", "destCidr": "************/27", "syslogEnabled": false}, {"comment": "<PERSON><PERSON> to Corp", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "*************/27", "destPort": "Any", "destCidr": "**********/25", "syslogEnabled": true}, {"comment": "Deny Corp to Guest", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "**********/25", "destPort": "Any", "destCidr": "*************/27", "syslogEnabled": false}, {"comment": "<PERSON><PERSON> to VDS", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "*************/27", "destPort": "Any", "destCidr": "************/27", "syslogEnabled": false}, {"comment": "<PERSON><PERSON> to Guest", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "************/27", "destPort": "Any", "destCidr": "*************/27", "syslogEnabled": false}, {"comment": "Deny DMZ to Corp", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********10/32,***********15/32,***********01/32", "destPort": "Any", "destCidr": "**********/25", "syslogEnabled": true}, {"comment": "Deny DMZ to VDS", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********15/32,***********01/32", "destPort": "Any", "destCidr": "************/27", "syslogEnabled": true}, {"comment": "Deny VDS to DMZ", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "************/27", "destPort": "Any", "destCidr": "***********02/32,***********00/32,***********10/32,***********15/32,***********01/32", "syslogEnabled": true}, {"comment": "<PERSON><PERSON> to Guest", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********02/32,***********00/32,***********15/32,***********01/32", "destPort": "Any", "destCidr": "*************/27", "syslogEnabled": true}, {"comment": "<PERSON><PERSON> to DMZ", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "*************/27", "destPort": "Any", "destCidr": "***********02/32,***********00/32,***********10/32,***********15/32,***********01/32", "syslogEnabled": true}, {"comment": "Deny DMZ to local nets", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "***********10/32,***********01/32", "destPort": "Any", "destCidr": "**********/25,************/27,*************/27", "syslogEnabled": true}, {"comment": "Genesis", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "**********/24", "destPort": "8061", "destCidr": "Any", "syslogEnabled": true}, {"comment": "block *************/32", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "*************/32", "syslogEnabled": true}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": []}