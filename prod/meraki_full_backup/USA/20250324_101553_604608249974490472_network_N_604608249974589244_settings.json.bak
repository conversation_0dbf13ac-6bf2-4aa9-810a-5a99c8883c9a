{"alerts": {"defaultDestinations": {"emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "snmp": false, "allAdmins": false, "httpServerIds": []}, "alerts": [{"type": "ampMalwareDetected", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": true, "httpServerIds": []}, "filters": {}}, {"type": "ampMalwareBlocked", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "applianceDown", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 10}}, {"type": "failoverEvent", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcpNoLeases", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "rogueDhcp", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "ipConflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "ip6Conflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcp6naRenumber", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcp6pdRenumber", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "cellularUpDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "clientConnectivity", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"clients": []}}, {"type": "vrrp", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "vpnConnectivityChange", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "settingsChanged", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"threshold": 104857600, "period": 1200}}, {"type": "weeklyUmbrella", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "prefixStarvation", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}], "muting": {}}, "snmp": {"access": "community", "communityString": "gls1stgrp", "users": []}, "syslog": {"servers": [{"host": "**********", "port": "514", "transportProtocol": "UDP", "encryption": {"enabled": false, "certificate": {"id": ""}}, "roles": ["Flows", "URLs", "Security events", "Appliance event log"]}]}, "firmware": {"products": {"appliance": {"currentVersion": {"id": "3089", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2024-06-05T23:12:07Z", "shortName": "MX 18.211.2"}, "lastUpgrade": {"time": "2024-07-16T03:05:09Z", "fromVersion": {"id": "3020", "firmware": "wired-18-2-11", "releaseType": "legacy", "releaseDate": "2024-05-08T22:57:12Z", "shortName": "MX 18.211"}, "toVersion": {"id": "3089", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2024-06-05T23:12:07Z", "shortName": "MX 18.211.2"}}, "nextUpgrade": {"time": "", "toVersion": {}}, "availableVersions": [{"id": "4625", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2025-03-10T19:27:18Z", "shortName": "MX **********"}, {"id": "4727", "firmware": "wired-19-1-7", "releaseType": "candidate", "releaseDate": "2025-03-19T07:25:34Z", "shortName": "MX ********"}], "participateInNextBetaRelease": false}}, "timezone": "US/Eastern", "upgradeWindow": {"dayOfWeek": "Sun", "hourOfDay": "4:00"}}}