{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "************", "address": "************/30", "nameservers": {"addresses": ["***********", "***********"]}}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": {"perfScore": 15.0}, "vlans": [{"id": 2, "networkId": "L_604608249974505652", "name": "Voice", "applianceIp": "*************", "subnet": "*************/26", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974593343"}, {"id": 10, "networkId": "L_604608249974505652", "name": "Data", "applianceIp": "***********", "subnet": "***********/25", "groupPolicyId": "102", "fixedIpAssignments": {"34:9f:7b:d0:ae:44": {"ip": "************"}}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n8.8.8.8\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974593340"}, {"id": 70, "networkId": "L_604608249974505652", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974593342"}, {"id": 75, "networkId": "L_604608249974505652", "name": "Guest Wifi", "applianceIp": "************29", "subnet": "************28/25", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974593344"}, {"id": 400, "networkId": "L_604608249974505652", "name": "Management", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974593345"}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,************/32,**************/32,***********/32,***********/32,***************/32,**************/32,*************/32,************/23,*************/32,*************/32", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,172.16.0.0/12,192.168.0.0/16", "destPort": "Any", "destCidr": "192.168.0.0/16", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "192.168.0.0/16", "destPort": "Any", "destCidr": "10.0.0.0/8,172.16.0.0/12,192.168.0.0/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": []}