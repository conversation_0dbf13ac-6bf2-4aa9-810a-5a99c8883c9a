[{"number": 0, "name": "Corp-WiFi", "enabled": true, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "8021x-radius", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa-eap", "wpaEncryptionMode": "WPA2 only", "radiusServers": [{"host": "*************", "port": 1812, "id": "604608249974616811", "radsecEnabled": false, "openRoamingCertificateId": null, "caCertificate": null}, {"host": "***********", "port": 1812, "id": "604608249974616813", "radsecEnabled": false, "openRoamingCertificateId": null, "caCertificate": null}], "radiusAccountingEnabled": false, "radiusTestingEnabled": true, "radiusServerTimeout": 1, "radiusServerAttemptsLimit": 3, "radiusFallbackEnabled": false, "radiusProxyEnabled": false, "radiusCoaEnabled": false, "radiusCalledStationId": "$NODE_MAC$:$VAP_NAME$", "radiusAuthenticationNasId": "$NODE_MAC$:$VAP_NUM$", "radiusAttributeForGroupPolicies": "Filter-Id", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 10, "radiusOverride": false, "minBitrate": 6, "bandSelection": "Dual band operation with Band Steering", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_Enterprise", "SSID_enterprise", "ssid_Enterprise", "ssid_enterprise"], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 1, "name": "Fleet", "enabled": true, "splashPage": "Click-through splash page", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA3 Transition Mode", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 70, "adminSplashUrl": "", "splashTimeout": "129600 minutes", "walledGardenEnabled": false, "minBitrate": 6, "bandSelection": "Dual band operation with Band Steering", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_FLEET", "SSID_Fleet", "ssid_Fleet", "ssid_fleet"], "speedBurst": {"enabled": false}}, {"number": 2, "name": "Guest", "enabled": true, "splashPage": "Click-through splash page", "ssidAdminAccessible": false, "authMode": "open", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 75, "adminSplashUrl": "", "splashTimeout": "1440 minutes", "walledGardenEnabled": false, "minBitrate": 6, "bandSelection": "Dual band operation with Band Steering", "perClientBandwidthLimitUp": 500, "perClientBandwidthLimitDown": 500, "perSsidBandwidthLimitUp": 1024, "perSsidBandwidthLimitDown": 1024, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["ssid_guest"], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 3, "name": "IOT", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA3 Transition Mode", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 10, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["ssid_iot"], "speedBurst": {"enabled": false}}, {"number": 4, "name": "NETGEAR66", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA3 Transition Mode", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 80, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["mps"], "speedBurst": {"enabled": false}}, {"number": 5, "name": "<PERSON><PERSON><PERSON>", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 80, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["ssid_edulog"], "speedBurst": {"enabled": false}}, {"number": 6, "name": "DriverHub", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 85, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["ssid_driver"], "speedBurst": {"enabled": false}}, {"number": 7, "name": "FSTU", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 82, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": false, "availableOnAllAps": false, "availabilityTags": ["SSID_12719", "SSID_12719A", "SSID_12739D"], "speedBurst": {"enabled": false}}, {"number": 8, "name": "F-SEC", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 40, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["ssid_f-sec"], "speedBurst": {"enabled": false}}, {"number": 9, "name": "TD-WiFi", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": true, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA3 Transition Mode", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 70, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": false, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}}, {"number": 10, "name": "TDC", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "8021x-radius", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa-eap", "wpaEncryptionMode": "WPA2 only", "radiusServers": [{"host": "***********", "port": 1812, "id": "604608249974622579", "radsecEnabled": false, "openRoamingCertificateId": null, "caCertificate": null}], "radiusAccountingEnabled": false, "radiusTestingEnabled": false, "radiusServerTimeout": 1, "radiusServerAttemptsLimit": 3, "radiusFallbackEnabled": false, "radiusProxyEnabled": false, "radiusCoaEnabled": false, "radiusCalledStationId": "$NODE_MAC$:$VAP_NAME$", "radiusAuthenticationNasId": "$NODE_MAC$:$VAP_NUM$", "radiusAttributeForGroupPolicies": "Filter-Id", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 10, "radiusOverride": true, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_TDC"], "speedBurst": {"enabled": false}, "psk": "*************"}, {"number": 11, "name": "TDCMigrate", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 10, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}}, {"number": 12, "name": "TDC_GUEST", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 75, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 500, "perClientBandwidthLimitDown": 500, "perSsidBandwidthLimitUp": 1024, "perSsidBandwidthLimitDown": 1024, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": false, "availabilityTags": ["SSID_TDC_GUEST"], "speedBurst": {"enabled": false}}, {"number": 13, "name": "TDVendor", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "psk", "psk": "*************", "dot11w": {"enabled": false, "required": false}, "dot11r": {"enabled": false, "adaptive": false}, "encryptionMode": "wpa", "wpaEncryptionMode": "WPA2 only", "ipAssignmentMode": "Bridge mode", "useVlanTagging": true, "defaultVlanId": 80, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "lanIsolationEnabled": false, "visible": true, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}}, {"number": 14, "name": "Unconfigured SSID 15", "enabled": false, "splashPage": "None", "ssidAdminAccessible": false, "authMode": "open", "ipAssignmentMode": "NAT mode", "adultContentFilteringEnabled": false, "dnsRewrite": {"enabled": false, "dnsCustomNameservers": []}, "minBitrate": 1, "bandSelection": "Dual band operation", "perClientBandwidthLimitUp": 0, "perClientBandwidthLimitDown": 0, "perSsidBandwidthLimitUp": 0, "perSsidBandwidthLimitDown": 0, "mandatoryDhcpEnabled": false, "visible": true, "availableOnAllAps": true, "availabilityTags": [], "speedBurst": {"enabled": false}, "psk": "*************"}]