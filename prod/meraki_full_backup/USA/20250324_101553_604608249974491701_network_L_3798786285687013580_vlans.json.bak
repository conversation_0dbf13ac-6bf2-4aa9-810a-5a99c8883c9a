[{"id": 1, "networkId": "L_3798786285687013580", "name": "<PERSON><PERSON><PERSON>", "applianceIp": "***********", "subnet": "***********/25", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "***********", "end": "***********1", "comment": "Reserved"}, {"start": "*************", "end": "*************", "comment": "Reserved"}, {"start": "*************", "end": "*************", "comment": "AP reservation"}, {"start": "*************", "end": "*************", "comment": "Reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n8.8.8.8\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974507913", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "L_3798786285687013580", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974507914", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "L_3798786285687013580", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "102", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974507912", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "L_3798786285687013580", "name": "MGMT", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974553767", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}]