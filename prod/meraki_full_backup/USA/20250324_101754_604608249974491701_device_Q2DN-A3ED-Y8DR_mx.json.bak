{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "************", "address": "************/29", "nameservers": {"addresses": ["************", "************"]}}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "dynamic"}}, "pppoe": {"enabled": false}}}}, "performance": {"perfScore": 21.0}, "vlans": [{"id": 1, "networkId": "L_604608249974506564", "name": "Data", "applianceIp": "**********", "subnet": "**********/25", "groupPolicyId": "103", "fixedIpAssignments": {"24:be:05:01:11:bd": {"ip": "***********", "name": null}, "58:20:b1:02:fc:7c": {"ip": "**********19", "name": null}, "58:20:b1:02:fc:80": {"ip": "**********18", "name": null}, "a0:48:1c:8c:8b:25": {"ip": "***********", "name": null}}, "reservedIpRanges": [{"start": "**********", "end": "**********1", "comment": "Reserved"}, {"start": "**********2", "end": "***********", "comment": "Cameras"}, {"start": "**********00", "end": "**********27", "comment": "Reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "12 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974504601"}, {"id": 2, "networkId": "L_604608249974506564", "name": "Voice", "applianceIp": "**********88", "subnet": "**********28/26", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "**********29", "end": "**********90", "comment": "Vlan 102"}], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974504600"}, {"id": 70, "networkId": "L_604608249974506564", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974519057"}, {"id": 75, "networkId": "L_604608249974506564", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974520300"}, {"id": 400, "networkId": "L_604608249974506564", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974553440"}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,************/32,**************/32,***********/32,***********/32,***************/32,**************/32,*************/32,************/23,*************/32,*************/32", "syslogEnabled": false}, {"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,*************/32,*************/32,*************/32", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,172.16.0.0/12,192.168.0.0/16", "destPort": "Any", "destCidr": "192.168.0.0/16", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "192.168.0.0/16", "destPort": "Any", "destCidr": "10.0.0.0/8,172.16.0.0/12,192.168.0.0/16", "syslogEnabled": false}, {"comment": "Jump Rule for Phone System", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "**********/25", "destPort": "Any", "destCidr": "**********90/32", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": [{"id": "e3c0e25e-34d8-440a-b29a-b12ec46a300c", "networkId": "L_604608249974506564", "enabled": true, "name": "Voice Management", "subnet": "*********/32", "gatewayIp": "**********90", "fixedIpAssignments": {}, "reservedIpRanges": [], "gatewayVlanId": null}]}