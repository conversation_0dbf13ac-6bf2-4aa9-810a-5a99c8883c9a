[{"id": 1, "networkId": "N_604608249974591272", "name": "Data", "applianceIp": "**********", "subnet": "**********/25", "groupPolicyId": "104", "fixedIpAssignments": {"84:69:93:0a:ae:7d": {"ip": "***********", "name": null}}, "reservedIpRanges": [{"start": "**********", "end": "***********", "comment": "Static Reservation"}, {"start": "**********00", "end": "**********06", "comment": "Radios"}, {"start": "**********07", "end": "**********17", "comment": "Reserved"}, {"start": "**********18", "end": "**********20", "comment": "Access Points"}, {"start": "**********21", "end": "**********27", "comment": "End"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "12 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974602211"}, {"id": 2, "networkId": "N_604608249974591272", "name": "Voice", "applianceIp": "**********29", "subnet": "**********28/26", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "**********29", "end": "**********38", "comment": "Static Reservation"}, {"start": "**********35", "end": "**********35", "comment": "Fuel System"}, {"start": "**********36", "end": "**********36", "comment": "Fuel System"}, {"start": "**********70", "end": "**********70", "comment": "Key System"}], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "12 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249974602213"}, {"id": 20, "networkId": "N_604608249974591272", "name": "p2p", "applianceIp": "************", "subnet": "************/30", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974602212"}, {"id": 70, "networkId": "N_604608249974591272", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974602216"}, {"id": 75, "networkId": "N_604608249974591272", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974602215"}, {"id": 101, "networkId": "N_604608249974591272", "name": "Data 2", "applianceIp": "***********", "subnet": "***********/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974602217"}, {"id": 400, "networkId": "N_604608249974591272", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974602218"}]