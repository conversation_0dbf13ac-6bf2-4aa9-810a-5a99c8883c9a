{"alerts": {"defaultDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "alerts": [{"type": "ampMalwareDetected", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": true, "httpServerIds": []}, "filters": {}}, {"type": "ampMalwareBlocked", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "applianceDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60}}, {"type": "failoverEvent", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcpNoLeases", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "rogueDhcp", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "ipConflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "cellularUpDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "clientConnectivity", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"clients": []}}, {"type": "vrrp", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "vpnConnectivityChange", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "settingsChanged", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"threshold": 104857600, "period": 1200}}, {"type": "weeklyUmbrella", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}], "muting": {}}, "snmp": {"access": "community", "communityString": "gls1stgrp", "users": []}, "syslog": {"servers": [{"host": "**********", "port": "514", "transportProtocol": "UDP", "encryption": {"enabled": false, "certificate": {"id": ""}}, "roles": ["Flows", "URLs", "Security events", "Appliance event log"]}]}, "firmware": {"products": {"appliance": {"currentVersion": {"id": "2241", "firmware": "wired-15-44", "releaseType": "legacy", "releaseDate": "2021-08-31T00:39:54Z", "shortName": "MX 15.44"}, "lastUpgrade": {"time": "2021-09-14T13:20:54Z", "fromVersion": {"id": "2155", "firmware": "wired-15-42-1", "releaseType": "stable", "releaseDate": "2021-04-08T01:30:34Z", "shortName": "MX 15.42.1"}, "toVersion": {"id": "2241", "firmware": "wired-15-44", "releaseType": "legacy", "releaseDate": "2021-08-31T00:39:54Z", "shortName": "MX 15.44"}}, "nextUpgrade": {"time": "", "toVersion": {}}, "availableVersions": [{"id": "4625", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2025-03-10T19:27:18Z", "shortName": "MX **********"}, {"id": "4727", "firmware": "wired-19-1-7", "releaseType": "candidate", "releaseDate": "2025-03-19T07:25:34Z", "shortName": "MX ********"}], "participateInNextBetaRelease": false}}, "timezone": "America/Los_Angeles", "upgradeWindow": {"dayOfWeek": "<PERSON><PERSON>", "hourOfDay": "3:00"}}}