[{"id": 2, "networkId": "N_604608249974589244", "name": "Voice", "applianceIp": "**************", "subnet": "**************/26", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "**************", "end": "**************", "comment": "Reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.64.5\n10.40.64.4\n10.40.13.3\n", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974600083", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 10, "networkId": "N_604608249974589244", "name": "Data", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n10.40.64.5\n10.40.64.4\n", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974600080", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "N_604608249974589244", "name": "Fleet Wifi", "applianceIp": "**********", "subnet": "**********/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974600082", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "N_604608249974589244", "name": "Guest Wifi", "applianceIp": "**************", "subnet": "**************/25", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974600084", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 105, "networkId": "N_604608249974589244", "name": "DriveCam", "applianceIp": "*************", "subnet": "*************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249977132971", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "N_604608249974589244", "name": "Management", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974600085", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}]