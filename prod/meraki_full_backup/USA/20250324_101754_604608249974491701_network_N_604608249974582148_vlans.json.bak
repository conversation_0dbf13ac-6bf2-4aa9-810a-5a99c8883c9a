[{"id": 1, "networkId": "N_604608249974582148", "name": "Data", "applianceIp": "***********", "subnet": "***********/25", "groupPolicyId": "104", "fixedIpAssignments": {"74:bf:c0:df:9a:0c": {"ip": "***********7", "name": null}}, "reservedIpRanges": [{"start": "***********", "end": "***********1", "comment": "reserved"}], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n8.8.8.8\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249974592100", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 70, "networkId": "N_604608249974582148", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/27", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974592101", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 75, "networkId": "N_604608249974582148", "name": "Guest Wifi", "applianceIp": "*************", "subnet": "*************/27", "groupPolicyId": "102", "fixedIpAssignments": {}, "reservedIpRanges": [{"start": "*************", "end": "*************", "comment": "clock"}], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974592102", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 400, "networkId": "N_604608249974582148", "name": "MGMT", "applianceIp": "*************", "subnet": "*************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974592103", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}]