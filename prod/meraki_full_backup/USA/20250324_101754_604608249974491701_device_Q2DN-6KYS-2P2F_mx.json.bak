{"uplinks": {"interfaces": {"wan1": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "**************", "address": "**************/29", "nameservers": {"addresses": ["************", "************"]}}}, "pppoe": {"enabled": false}}, "wan2": {"enabled": true, "vlanTagging": {"enabled": false}, "svis": {"ipv4": {"assignmentMode": "static", "gateway": "*************", "address": "*************/30", "nameservers": {"addresses": ["*************", "************"]}}}, "pppoe": {"enabled": false}}}}, "performance": {"perfScore": 31.0}, "vlans": [{"id": 10, "networkId": "L_604608249974505272", "name": "Data", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}, {"type": "text", "code": "160", "value": "https://phone-home.usw2.pure.cloud"}], "interfaceId": "604608249975029927"}, {"id": 70, "networkId": "L_604608249974505272", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249975382894"}, {"id": 75, "networkId": "L_604608249974505272", "name": "Guest", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "3798786285687083529"}, {"id": 82, "networkId": "L_604608249974505272", "name": "FSTU", "applianceIp": "************", "subnet": "************/23", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974609245"}, {"id": 102, "networkId": "L_604608249974505272", "name": "DMZ", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249975134306"}, {"id": 200, "networkId": "L_604608249974505272", "name": "Adept", "applianceIp": "************", "subnet": "************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249975025464"}, {"id": 400, "networkId": "L_604608249974505272", "name": "MGMT", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974587855"}], "firewall": {"rules": [{"comment": "Block Malware", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "**************/32,************/32,**************/32,***********/32,***********/32,***************/32,**************/32,*************/32,************/23,*************/32,*************/32", "syslogEnabled": false}, {"comment": "Block DMZ to local nets", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "**************/28", "destPort": "Any", "destCidr": "10.0.0.0/8,**********/12,***********/16", "syslogEnabled": false}, {"comment": "Adept", "policy": "allow", "protocol": "tcp", "srcPort": "Any", "srcCidr": "************/25", "destPort": "80,139,443,445,631,1433,3911,9100,50656", "destCidr": "************/24", "syslogEnabled": false}, {"comment": "Adept", "policy": "allow", "protocol": "udp", "srcPort": "Any", "srcCidr": "************/25", "destPort": "1434", "destCidr": "************/24", "syslogEnabled": false}, {"comment": "Adept ping", "policy": "allow", "protocol": "icmp", "srcPort": "Any", "srcCidr": "************/25", "destPort": "Any", "destCidr": "************/24", "syslogEnabled": false}, {"comment": "Block TCP Ports", "policy": "deny", "protocol": "tcp", "srcPort": "Any", "srcCidr": "Any", "destPort": "135,137,138,139,445,6660,6661,6662,6663,6664,6665,6666,6667,6668,6669", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block UDP Ports", "policy": "deny", "protocol": "udp", "srcPort": "Any", "srcCidr": "Any", "destPort": "69,135,137,138,139,161,162,514", "destCidr": "Any", "syslogEnabled": false}, {"comment": "Block Insecure to RFC1918", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "***********/16", "destPort": "Any", "destCidr": "10.0.0.0/8,**********/12,***********/16", "syslogEnabled": false}, {"comment": "Block RFC1918 to Insecure", "policy": "deny", "protocol": "any", "srcPort": "Any", "srcCidr": "10.0.0.0/8,**********/12,***********/16", "destPort": "Any", "destCidr": "***********/16", "syslogEnabled": false}, {"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "routes": []}