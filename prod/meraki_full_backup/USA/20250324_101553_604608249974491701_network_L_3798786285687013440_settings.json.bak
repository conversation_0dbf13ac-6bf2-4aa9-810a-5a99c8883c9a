{"alerts": {"defaultDestinations": {"emails": [], "snmp": false, "allAdmins": true, "httpServerIds": []}, "alerts": [{"type": "ampMalwareDetected", "enabled": true, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": true, "httpServerIds": []}, "filters": {}}, {"type": "ampMalwareBlocked", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "applianceDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60}}, {"type": "failoverEvent", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcpNoLeases", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "rogueDhcp", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "ipConflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "ip6Conflict", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcp6naRenumber", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "dhcp6pdRenumber", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "cellularUpDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "clientConnectivity", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"clients": []}}, {"type": "vrrp", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "vpnConnectivityChange", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "settingsChanged", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"threshold": 104857600, "period": 1200}}, {"type": "weeklyUmbrella", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "prefixStarvation", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {}}, {"type": "cellularGatewayDown", "enabled": false, "alertDestinations": {"emails": [], "snmp": false, "allAdmins": false, "httpServerIds": []}, "filters": {"timeout": 60}}], "muting": {}}, "snmp": {"access": "community", "communityString": "gls1stgrp", "users": []}, "syslog": {"servers": [{"host": "**********", "port": "514", "transportProtocol": "UDP", "encryption": {"enabled": false, "certificate": {"id": ""}}, "roles": ["Flows", "URLs", "Security events", "Appliance event log"]}]}, "firmware": {"products": {"appliance": {"currentVersion": {"id": "3089", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2024-06-05T23:12:07Z", "shortName": "MX 18.211.2"}, "lastUpgrade": {"time": "2024-06-25T02:30:39Z", "fromVersion": {"id": "2277", "firmware": "wired-16-16", "releaseType": "legacy", "releaseDate": "2022-02-28T17:22:52Z", "shortName": "MX 16.16"}, "toVersion": {"id": "3089", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2024-06-05T23:12:07Z", "shortName": "MX 18.211.2"}}, "nextUpgrade": {"time": "", "toVersion": {}}, "availableVersions": [{"id": "4625", "firmware": "wired-18-2-11", "releaseType": "stable", "releaseDate": "2025-03-10T19:27:18Z", "shortName": "MX **********"}, {"id": "4727", "firmware": "wired-19-1-7", "releaseType": "candidate", "releaseDate": "2025-03-19T07:25:34Z", "shortName": "MX ********"}], "participateInNextBetaRelease": false}, "cellularGateway": {"currentVersion": {"id": "2885", "firmware": "Custom version (f0a95b5)", "releaseType": "stable", "releaseDate": "2024-06-25T18:12:17Z", "shortName": "MG 3.102"}, "lastUpgrade": {"time": "2024-07-03T01:10:18Z", "fromVersion": {"id": "2406", "firmware": "Custom version (9b5f3f3)", "releaseType": "stable", "releaseDate": "2023-03-10T00:49:29Z", "shortName": "MG 3.0"}, "toVersion": {"id": "2885", "firmware": "Custom version (f0a95b5)", "releaseType": "stable", "releaseDate": "2024-06-25T18:12:17Z", "shortName": "MG 3.102"}}, "nextUpgrade": {"time": "", "toVersion": {}}, "availableVersions": [{"id": "3842", "firmware": "gateway-3-212-1", "releaseType": "stable", "releaseDate": "2025-03-18T21:26:36Z", "shortName": "MG 3.212.1"}, {"id": "4427", "firmware": "gateway-3-213", "releaseType": "beta", "releaseDate": "2025-03-18T21:24:33Z", "shortName": "MG 3.213"}], "participateInNextBetaRelease": false}}, "timezone": "US/Eastern", "upgradeWindow": {"dayOfWeek": "Sun", "hourOfDay": "2:00"}}}