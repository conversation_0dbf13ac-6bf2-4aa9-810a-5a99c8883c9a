[{"id": 2, "networkId": "N_604608249974583503", "name": "Voice", "applianceIp": "*************", "subnet": "*************/26", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974593973"}, {"id": 10, "networkId": "N_604608249974583503", "name": "Data", "applianceIp": "***********", "subnet": "***********/25", "groupPolicyId": "102", "fixedIpAssignments": {"34:9f:7b:59:21:bb": {"ip": "************"}, "34:9f:7b:59:39:e5": {"ip": "***********1"}, "34:9f:7b:59:bd:e7": {"ip": "************"}, "34:9f:7b:5a:1a:8c": {"ip": "************"}, "74:bf:c0:47:e6:43": {"ip": "************"}, "74:bf:c0:48:23:a8": {"ip": "************"}, "a0:2b:b8:65:7a:49": {"ip": "***********6"}, "f8:0d:60:9d:f3:ca": {"ip": "***********"}}, "reservedIpRanges": [], "dnsNameservers": "************\n10.40.252.12\n10.40.13.3\n8.8.8.8\n8.8.4.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "vt.dom"}], "interfaceId": "604608249974593970"}, {"id": 40, "networkId": "N_604608249974583503", "name": "Surveillance", "applianceIp": "************", "subnet": "************/24", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 day", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249975861935"}, {"id": 70, "networkId": "N_604608249974583503", "name": "Fleet Wifi", "applianceIp": "************", "subnet": "************/25", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "1 hour", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974593972"}, {"id": 75, "networkId": "N_604608249974583503", "name": "Guest Wifi", "applianceIp": "************29", "subnet": "************28/25", "groupPolicyId": "101", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "google_dns", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "30 minutes", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "604608249974593974"}, {"id": 400, "networkId": "N_604608249974583503", "name": "Management", "applianceIp": "**************", "subnet": "**************/28", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "***********\n10.158.16.27", "dhcpHandling": "Do not respond to DHCP requests", "interfaceId": "604608249974593975"}]