#!/usr/bin/env python3
"""
Script: meraki_dhcp_reservations.py

Lists all DHCP reservations across Meraki networks and cross-references with UniFi APs
"""

import sys
import requests
from typing import List, Dict
import meraki
from tabulate import tabulate
import time
# Meraki Configuration
MERAKI_API_KEY = "****************************************"

# UniFi Configuration
UNIFI_HOST = "https://unifi.limocar.int:8443"
USERNAME = "blos01"  # Alrsadye ttstbl01
VERIFY_SSL = False

class DHCPReservation:
    def __init__(self, site: str, vlan_id: str, vlan_name: str, mac: str, ip: str, name: str, unifi_name: str = ""):
        self.site = site
        self.vlan_id = vlan_id
        self.vlan_name = vlan_name
        self.mac = mac
        self.ip = ip
        self.name = name
        self.unifi_name = unifi_name

    def to_row(self) -> List[str]:
        return [
            self.site,
            self.vlan_id,
            self.vlan_name,
            self.mac,
            self.ip,
            self.name,
            self.unifi_name
        ]

def login_unifi(session: requests.Session, password: str) -> bool:
    """Login to UniFi Controller"""
    login_url = f"{UNIFI_HOST}/api/login"
    payload = {
        "username": USERNAME,
        "password": password
    }
    resp = session.post(login_url, json=payload, verify=VERIFY_SSL)
    return resp.status_code == 200

def get_sites(session: requests.Session) -> List[Dict]:
    """Get all sites from the controller"""
    url = UNIFI_HOST + "/api/self/sites"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    # Fallback for older controllers
    fallback_url = UNIFI_HOST + "/api/stat/sites"
    resp = session.get(fallback_url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    return []

def get_all_aps(session: requests.Session) -> List[Dict]:
    """Get all APs from all sites"""
    ap_data = []
    sites = get_sites(session)
    
    for site in sites:
        site_name = site["name"]
        url = f"{UNIFI_HOST}/api/s/{site_name}/stat/device"
        resp = session.get(url, verify=VERIFY_SSL)
        if resp.status_code == 200:
            devices = resp.json().get("data", [])
            # Filter for only APs (type "uap") that are:
            # 1. Actually connected (state = 1)
            # 2. Have a valid IP address
            # 3. Have been seen recently (within last hour)
            aps = [
                dev for dev in devices 
                if dev.get("type") == "uap" 
                and dev.get("state", 0) == 1
                and dev.get("ip")
                and dev.get("last_seen", 0) > (time.time() - 3600)  # seen within last hour
            ]
            ap_data.extend(aps)
    
    # Additional verification step - remove duplicate IPs
    seen_ips = set()
    verified_aps = []
    for ap in ap_data:
        ip = ap.get("ip")
        if ip and ip not in seen_ips:
            seen_ips.add(ip)
            verified_aps.append(ap)
    
    return verified_aps

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def get_dhcp_reservations(dashboard: meraki.DashboardAPI, unifi_aps: List[Dict]) -> tuple[List[DHCPReservation], List[Dict]]:
    """Collect all DHCP reservations and cross-reference with UniFi APs"""
    reservations = []
    ap_mac_to_name = {ap.get("mac").lower(): ap.get("name", "Unnamed AP") for ap in unifi_aps}
    reserved_macs = set()
    
    try:
        orgs = dashboard.organizations.getOrganizations()
        
        for org in orgs:
            print(f"Processing organization: {org['name']}")
            
            try:
                networks = dashboard.organizations.getOrganizationNetworks(org['id'])
                
                for net in networks:
                    if 'appliance' in net.get('productTypes', []):
                        print(f"  Processing network: {net['name']}")
                        
                        try:
                            vlans = dashboard.appliance.getNetworkApplianceVlans(net['id'])
                            
                            for vlan in vlans:
                                try:
                                    vlan_details = dashboard.appliance.getNetworkApplianceVlan(
                                        net['id'],
                                        vlan['id']
                                    )
                                    
                                    if 'fixedIpAssignments' in vlan_details:
                                        for mac, config in vlan_details['fixedIpAssignments'].items():
                                            mac_lower = mac.lower()
                                            reserved_macs.add(mac_lower)
                                            unifi_name = ap_mac_to_name.get(mac_lower, "")
                                            
                                            reservations.append(DHCPReservation(
                                                site=net['name'],
                                                vlan_id=str(vlan['id']),
                                                vlan_name=vlan.get('name', 'Default'),
                                                mac=mac,
                                                ip=config.get('ip', 'N/A'),
                                                name=config.get('name', 'N/A'),
                                                unifi_name=unifi_name
                                            ))
                                            
                                except meraki.APIError as e:
                                    print(f"    Error getting VLAN {vlan['id']} details: {e}")
                                    
                        except meraki.APIError as e:
                            print(f"    Error getting VLANs for network {net['name']}: {e}")
                            
            except meraki.APIError as e:
                print(f"  Error getting networks for org {org['name']}: {e}")
                
    except meraki.APIError as e:
        print(f"Error accessing Meraki organizations: {e}")
    
    # Find APs without reservations
    unreserved_aps = [ap for ap in unifi_aps if ap.get("mac").lower() not in reserved_macs]
    
    return reservations, unreserved_aps

def get_site_name_mapping(session: requests.Session) -> Dict[str, str]:
    """Create mapping of site IDs to descriptive names"""
    sites = get_sites(session)
    return {site["name"]: site.get("desc", site["name"]) for site in sites}

def get_network_mapping(dashboard: meraki.DashboardAPI) -> Dict[str, Dict]:
    """Create mapping of IP subnets to network names and MX names"""
    network_mapping = {}
    try:
        orgs = dashboard.organizations.getOrganizations()
        for org in orgs:
            networks = dashboard.organizations.getOrganizationNetworks(org['id'])
            for net in networks:
                if 'appliance' in net.get('productTypes', []):
                    # Get devices in the network to find MX
                    devices = dashboard.networks.getNetworkDevices(net['id'])
                    mx_device = next((dev for dev in devices if dev['model'].startswith('MX')), None)
                    
                    if not mx_device:
                        continue
                        
                    mx_name = mx_device['name']
                    
                    # Get VLANs to map IP subnets
                    try:
                        vlans = dashboard.appliance.getNetworkApplianceVlans(net['id'])
                        for vlan in vlans:
                            subnet = vlan.get('subnet')
                            if subnet:
                                # Add additional metadata to help with verification
                                network_mapping[subnet] = {
                                    'network_name': net['name'],
                                    'mx_name': mx_name,
                                    'vlan_id': vlan.get('id'),
                                    'vlan_name': vlan.get('name'),
                                    'mx_serial': mx_device.get('serial')
                                }
                    except meraki.APIError:
                        continue
    except meraki.APIError as e:
        print(f"Error getting network mapping: {e}")
    
    return network_mapping

def find_network_for_ip(ip: str, network_mapping: Dict[str, Dict]) -> tuple:
    """Find the network name and MX name for a given IP address"""
    import ipaddress
    ip_obj = ipaddress.ip_address(ip)
    
    for subnet, info in network_mapping.items():
        try:
            network = ipaddress.ip_network(subnet)
            if ip_obj in network:
                return info['network_name'], info['mx_name']
        except ValueError:
            continue
    
    return "Unknown Network", "Unknown MX"

def main():
    # Setup UniFi connection
    print("\nConnecting to UniFi Controller...")
    session = requests.Session()
    if not VERIFY_SSL:
        requests.packages.urllib3.disable_warnings()
    
    password = getpass.getpass("Enter UniFi blos01 password: ")
    if not login_unifi(session, password):
        print("Failed to login to UniFi Controller")
        return
    
    # Get all UniFi APs
    print("Collecting UniFi AP information...")
    unifi_aps = get_all_aps(session)
    
    # Setup Meraki connection
    print("\nConnecting to Meraki dashboard...")
    dashboard = setup_meraki()
    
    # Get network mapping from Meraki
    print("Getting Meraki network information...")
    network_mapping = get_network_mapping(dashboard)
    
    # Get site name mapping from UniFi
    site_mapping = get_site_name_mapping(session)
    
    # Get reservations and unreserved APs
    print("Collecting DHCP reservations...")
    reservations, unreserved_aps = get_dhcp_reservations(dashboard, unifi_aps)
    
    if not reservations:
        print("\nNo DHCP reservations found.")
        return
    
    # Sort reservations by site name and IP address
    reservations.sort(key=lambda x: (x.site, x.ip))
    
    # Display reservations
    headers = ["Site", "VLAN ID", "VLAN Name", "MAC Address", "Reserved IP", "Description", "UniFi AP Name"]
    rows = [reservation.to_row() for reservation in reservations]
    
    print("\nDHCP Reservations Report")
    print("=" * 100)
    print(tabulate(rows, headers=headers, tablefmt="simple"))
    print(f"\nTotal reservations found: {len(reservations)}")
    
    # Display unreserved APs with enhanced information
    if unreserved_aps:
        print("\nUniFi APs Without DHCP Reservations:")
        print("=" * 120)
        unreserved_headers = ["Site / Network", "MX Name", "MAC Address", "IP Address", "AP Model", "AP Name"]
        unreserved_rows = []
        
        for ap in unreserved_aps:
            ip_address = ap.get("ip", "N/A")
            network_name, mx_name = find_network_for_ip(ip_address, network_mapping)
            
            unreserved_rows.append([
                network_name,
                mx_name,
                ap.get("mac"),
                ip_address,
                ap.get("model", "Unknown Model"),
                ap.get("name", "Unnamed AP")
            ])
        
        # Sort rows by network name and IP address
        unreserved_rows.sort(key=lambda x: (x[0], x[3]))
        
        print(tabulate(unreserved_rows, headers=unreserved_headers, tablefmt="simple"))
        print(f"\nTotal APs without reservations: {len(unreserved_aps)}")

if __name__ == "__main__":
    import getpass
    main()
