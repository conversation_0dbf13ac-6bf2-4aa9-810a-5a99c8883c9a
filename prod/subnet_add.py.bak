#!/usr/bin/env python3
"""
Script to display all *********/16 subnets in use from Meraki dashboard and Netbox
"""

import ipaddress
from typing import List, Dict, NamedTuple
import meraki
import pynetbox
from tabulate import tabulate
import math
import sys
import tty
import termios

class NetworkSet(NamedTuple):
    voip: str
    lan: str
    me: str
    guest: str
    printer: str
    iot: str
    dvr: str
    mgmt: str

def generate_network_set(base_subnet: str) -> NetworkSet:
    """Generate all related subnets based on the chosen subnet"""
    # Extract the third octet and fourth octet/mask from base subnet
    octets = base_subnet.split('.')
    third_octet = octets[2]
    fourth_and_mask = octets[3]
    
    return NetworkSet(
        voip=f"192.168.{third_octet}.{fourth_and_mask}",
        lan=f"10.48.{third_octet}.{fourth_and_mask}",
        me=f"172.16.{third_octet}.{fourth_and_mask}",
        guest=f"10.1.{third_octet}.{fourth_and_mask}",
        printer=f"172.60.{third_octet}.{fourth_and_mask}",
        iot=f"172.70.{third_octet}.{fourth_and_mask}",
        dvr=f"172.80.{third_octet}.{fourth_and_mask}",
        mgmt=f"10.90.{third_octet}.{fourth_and_mask}"
    )

def display_sites_in_columns(sites: List, max_lines: int = 30):
    """Display sites in multiple columns"""
    # Calculate number of columns needed
    num_sites = len(sites)
    num_columns = math.ceil(num_sites / max_lines)
    lines_per_column = math.ceil(num_sites / num_columns)
    
    # Format each site entry
    formatted_sites = [f"{idx+1:3d} - {site.name}" for idx, site in enumerate(sites)]
    
    # Split into columns
    columns = []
    for i in range(num_columns):
        start = i * lines_per_column
        end = start + lines_per_column
        columns.append(formatted_sites[start:end])
    
    # Print columns side by side
    for row in range(lines_per_column):
        line = ""
        for col in columns:
            if row < len(col):
                line += f"{col[row]:<40}"
        print(line)

def get_single_keypress(prompt: str) -> str:
    """Get a single keypress with local echo"""
    print(prompt, end='', flush=True)
    fd = sys.stdin.fileno()
    old_settings = termios.tcgetattr(fd)
    try:
        tty.setraw(sys.stdin.fileno())
        ch = sys.stdin.read(1)
    finally:
        termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
    print(ch.upper())  # Echo the character
    return ch.upper()

def get_input(prompt: str) -> str:
    """Get input with local echo"""
    response = input(prompt)
    print(response)  # Echo the response
    return response

def create_netbox_subnets(nb: pynetbox.api, networks: NetworkSet):
    """Create subnets in Netbox"""
    choice = get_single_keypress("\nDo you want to associate the subnets with a site? (Y/N): ")
    
    site = None
    if choice == 'Y':
        sites = list(nb.dcim.sites.all())
        print("\nAvailable sites:")
        display_sites_in_columns(sites)
        
        while True:
            try:
                site_idx = int(get_input("\nEnter site number: ")) - 1
                if 0 <= site_idx < len(sites):
                    site = sites[site_idx]
                    break
                print("Invalid selection. Please try again.")
            except ValueError:
                print("Please enter a valid number.")
    
    # VLAN to name mapping
    vlan_names = {
        'voip': ('10', 'VoIP'),
        'lan': ('20', 'LAN'),
        'me': ('30', 'ME'),
        'guest': ('40', 'GUEST'),
        'printer': ('60', 'PRINTER'),
        'iot': ('70', 'IoT'),
        'dvr': ('80', 'DVR'),
        'mgmt': ('90', 'MGMT')
    }
    
    # Create prefixes
    for name, subnet in networks._asdict().items():
        vlan_num, network_name = vlan_names[name]
        try:
            nb.ipam.prefixes.create(
                prefix=subnet,
                site=site.id if site else None,
                status='active',
                description=f"Meraki VLAN {vlan_num} NAME: {network_name}"
            )
            print(f"Created prefix {subnet} for {name}")
        except Exception as e:
            print(f"Failed to create prefix {subnet}: {e}")

def display_firewall_config(networks: NetworkSet, name: str):
    """Display Fortigate firewall configuration"""
    print("\nFortigate Configuration Commands:")
    print(f"\nconfig firewall address")
    print(f'    edit "Network {name} TDC-LAN"')
    print(f'        set subnet {networks.lan}')
    print(f'        set comment "TDC LAN Network"')
    print(f'        set allow-routing enable')
    print(f'    next')
    print(f'    edit "Network {name} TDC-ME"')
    print(f'        set subnet {networks.me}')
    print(f'        set comment "TDC ME Network"')
    print(f'        set allow-routing enable')
    print(f'    next')
    print(f'end')
    
    print(f"\nconfig firewall addrgrp")
    print(f'    edit "TELUS-MERAKI-CORPO"')
    print(f'        append member "Network {name} TDC-LAN"')
    print(f'    next')
    print(f'    edit "TELUS-MERAKI-ME"')
    print(f'        append member "Network {name} TDC-ME"')
    print(f'    next')
    print(f'end')

def display_telus_config(networks: NetworkSet):
    """Display Telus network configuration"""
    print("\nTelus Network Configuration:")
    headers = ["Name", "Subnet", "VLAN"]
    rows = [
        ["VoIP", networks.voip, "VLAN 10"],
        ["LAN", networks.lan, "VLAN 20"],
        ["ME", networks.me, "VLAN 30"],
        ["GUEST", networks.guest, "VLAN 40"],
        ["Printer", networks.printer, "VLAN 60"],
        ["IoT", networks.iot, "VLAN 70"],
        ["Dvr", networks.dvr, "VLAN 80"],
        ["MGMT90", networks.mgmt, "VLAN 90"]
    ]
    print(tabulate(rows, headers=headers, tablefmt='simple'))

def post_network_selection_menu(selected_network: str, nb: pynetbox.api):
    """Display and handle post-network selection menu"""
    networks = generate_network_set(selected_network)
    
    while True:
        print("\nOptions:")
        print("1 - Create subnets in netbox")
        print("2 - Display firewall configs")
        print("3 - Display Telus configs")
        print("4 - Exit")
        
        choice = get_input("\nEnter your choice (1-4): ")
        
        if choice == '1':
            create_netbox_subnets(nb, networks)
        elif choice == '2':
            name = get_input("\nWhat is the name for firewall object (ex: Regina140): ")
            display_firewall_config(networks, name)
        elif choice == '3':
            display_telus_config(networks)
        elif choice == '4':
            print("\nGoodbye!")
            sys.exit(0)  # Exit the program completely
        else:
            print("Invalid choice. Please try again.")

# Meraki Configuration
MERAKI_API_KEY = "****************************************"
ORGANIZATION_SUBNET = ipaddress.IPv4Network("*********/16")

# Netbox Configuration
NETBOX_URL = "https://ansj7013.cloud.netboxapp.com"
NETBOX_TOKEN = "vXBuZYQp1X80lim}trYdenPbcW0x#nvf+1gOry#X"

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True,
        caller=None,  # Suppresses API key warnings
        simulate=False
    )

def setup_netbox() -> pynetbox.api:
    """Initialize Netbox API"""
    return pynetbox.api(
        url=NETBOX_URL,
        token=NETBOX_TOKEN
    )

def get_organization_subnets(dashboard: meraki.DashboardAPI) -> List[Dict]:
    """Get all subnets within *********/16 from Meraki networks"""
    subnet_list = []
    
    try:
        orgs = dashboard.organizations.getOrganizations()
        
        for org in orgs:
            networks = dashboard.organizations.getOrganizationNetworks(org['id'])
            
            for net in networks:
                if 'appliance' in net.get('productTypes', []):
                    try:
                        vlans = dashboard.appliance.getNetworkApplianceVlans(net['id'])
                        for vlan in vlans:
                            if 'subnet' in vlan:
                                try:
                                    subnet = ipaddress.IPv4Network(vlan['subnet'])
                                    if subnet.subnet_of(ORGANIZATION_SUBNET):
                                        subnet_list.append({
                                            'source': 'meraki',
                                            'site_name': net['name'],
                                            'subnet': str(subnet),
                                            'vlan_id': vlan.get('id', 'N/A'),
                                            'vlan_name': vlan.get('name', 'Default')
                                        })
                                except ValueError:
                                    continue
                    except meraki.APIError as e:
                        print(f"Error getting VLANs for {net['name']}: {e}")
                        
    except meraki.APIError as e:
        print(f"Error accessing Meraki organizations: {e}")
    
    return subnet_list

def get_netbox_subnets(nb: pynetbox.api) -> List[Dict]:
    """Get all subnets within *********/16 from Netbox"""
    subnet_list = []
    
    try:
        # Get all prefixes from Netbox
        prefixes = nb.ipam.prefixes.filter(within="*********/16")
        
        for prefix in prefixes:
            try:
                subnet = ipaddress.IPv4Network(prefix.prefix)
                site_name = prefix.site.name if prefix.site else "No Site"
                subnet_list.append({
                    'source': 'netbox',
                    'site_name': site_name,
                    'subnet': str(subnet),
                    'vlan_id': prefix.vlan.vid if prefix.vlan else 'N/A',
                    'vlan_name': prefix.vlan.name if prefix.vlan else 'N/A'
                })
            except ValueError:
                continue
                
    except Exception as e:
        print(f"Error accessing Netbox prefixes: {e}")
    
    return subnet_list

def combine_and_sort_subnets(meraki_subnets: List[Dict], netbox_subnets: List[Dict]) -> List[Dict]:
    """Combine Meraki and Netbox subnets, removing duplicates"""
    # Create a set of all subnets from Meraki
    meraki_subnet_set = {s['subnet'] for s in meraki_subnets}
    
    # Create combined list
    combined_subnets = meraki_subnets.copy()
    
    # Add Netbox subnets that don't exist in Meraki
    for subnet in netbox_subnets:
        if subnet['subnet'] not in meraki_subnet_set:
            combined_subnets.append(subnet)
    
    # Sort by subnet address
    return sorted(combined_subnets, key=lambda x: ipaddress.IPv4Network(x['subnet']))

def display_subnets_table(subnets: List[Dict]) -> str:
    """Format subnet information into a table"""
    headers = ["Site Name", "Subnet", "VLAN ID", "VLAN Name"]
    rows = []
    for s in subnets:
        prefix = "M-" if s['source'] == 'meraki' else "Netbox-"
        rows.append([
            f"{prefix}{s['site_name']}", 
            s['subnet'], 
            s['vlan_id'], 
            s['vlan_name']
        ])
    return tabulate(rows, headers=headers, tablefmt='simple')

def display_menu() -> str:
    """Display menu and return user selection"""
    print("\nMenu Options:")
    print("1 - Add a network")
    print("2 - Quit")
    while True:
        choice = input("\nEnter your choice (1-2): ")
        if choice in ['1', '2']:
            return choice
        print("Invalid choice. Please try again.")

def display_region_menu() -> str:
    """Display region menu and get selection"""
    print("\nSelect a region:")
    print("1 - Quebec (********* - 10.48.63.255)")
    print("2 - Ontario (********** - 10.48.127.255)")
    print("3 - West (*********** - 10.48.191.255)")
    print("4 - Free (*********** - 10.48.255.255)")
    print("5 - Quit")
    
    while True:
        choice = get_input("\nEnter your choice (1-5): ")
        if choice in ['1', '2', '3', '4', '5']:
            if choice == '5':
                print("\nGoodbye!")
                sys.exit(0)  # Exit the program completely
            return choice
        print("Invalid choice. Please try again.")

def display_network_size_menu() -> str:
    """Display network size selection menu"""
    print("\nSelect Network Size:")
    print("1 - /23 (512 ip)")
    print("2 - /24 (256 ip)")
    print("3 - /25 (128 ip)")
    print("4 - /26 (64 ip)")
    print("5 - /27 (32 ip)")
    
    while True:
        choice = input("\nEnter your choice (1-5): ")
        if choice in ['1', '2', '3', '4', '5']:
            return choice
        print("Invalid choice. Please try again.")

def get_region_range(region_choice: str) -> tuple:
    """Get start and end IP range for selected region"""
    ranges = {
        '1': ('*********', '**********'),    # QUEBEC
        '2': ('**********', '***********'),  # Ontario
        '3': ('***********', '***********'), # West
        '4': ('***********', '***********')  # FREE
    }
    return ranges.get(region_choice)

def get_network_size(size_choice: str) -> str:
    """Get network mask for selected size"""
    sizes = {
        '1': '/23',
        '2': '/24',
        '3': '/25',
        '4': '/26',
        '5': '/27'
    }
    return sizes.get(size_choice)

def find_free_networks(start_ip: str, end_ip: str, network_size: str, existing_subnets: List[Dict]) -> List[str]:
    """Find ALL available networks of specified size within range"""
    # Convert existing subnets to set of networks
    existing_networks = {ipaddress.ip_network(s['subnet']) for s in existing_subnets}
    
    # Create list of all possible networks of desired size in range
    start = ipaddress.ip_address(start_ip)
    end = ipaddress.ip_address(end_ip)
    network_mask = int(network_size.strip('/'))
    
    possible_networks = []
    current = ipaddress.ip_network(f"{start}/{network_mask}", strict=False)
    
    while current.network_address <= end:
        if not any(current.overlaps(existing) for existing in existing_networks):
            possible_networks.append(str(current))
        current = ipaddress.ip_network(f"{current.broadcast_address + 1}/{network_mask}", strict=False)
    
    return possible_networks

def get_surrounding_networks(network: str, existing_subnets: List[Dict], mask: int) -> tuple:
    """Get the networks in use immediately before and after the given network"""
    network_obj = ipaddress.ip_network(network)
    prev_net = None
    next_net = None
    
    # Convert all existing subnets to IPv4Network objects
    existing_nets = [ipaddress.ip_network(s['subnet']) for s in existing_subnets]
    existing_nets.sort()
    
    # Find previous and next networks
    for net in existing_nets:
        if net.network_address < network_obj.network_address:
            prev_net = net
        elif net.network_address > network_obj.network_address:
            next_net = net
            break
    
    return prev_net, next_net

def are_networks_contiguous(net1: str, net2: str) -> bool:
    """Check if two networks are contiguous"""
    n1 = ipaddress.ip_network(net1)
    n2 = ipaddress.ip_network(net2)
    return n1.broadcast_address + 1 == n2.network_address and n1.prefixlen == n2.prefixlen

def group_contiguous_networks(networks: List[str]) -> List[List[str]]:
    """Group contiguous networks together"""
    if not networks:
        return []
    
    groups = []
    current_group = [networks[0]]
    
    for i in range(1, len(networks)):
        if are_networks_contiguous(networks[i-1], networks[i]):
            current_group.append(networks[i])
        else:
            groups.append(current_group)
            current_group = [networks[i]]
    
    groups.append(current_group)
    return groups

def display_available_networks(networks: List[str], existing_subnets: List[Dict], region_start: str, region_end: str) -> str:
    """Display available networks with context and get user selection"""
    if not networks:
        print("\nNo available networks found in the selected range.")
        return None

    # Group contiguous networks
    network_groups = group_contiguous_networks(networks)
    current_page = 0
    networks_per_page = 10
    
    # Flatten groups for total count while preserving group information
    flat_networks = []
    for group in network_groups:
        flat_networks.extend(group)
    
    total_pages = (len(flat_networks) + networks_per_page - 1) // networks_per_page

    while True:
        print(f"\nShowing page {current_page + 1} of {total_pages}")
        print("Available Networks and Context:")
        print("(* indicates an available network)\n")
        
        # Calculate which networks to show on this page
        start_idx = current_page * networks_per_page
        end_idx = start_idx + networks_per_page
        
        # Track networks displayed for selection
        displayed_networks = []
        current_count = 0
        last_shown_context = None  # Track last shown context to avoid duplicates
        
        # Iterate through groups
        for group_idx, group in enumerate(network_groups):
            # Skip groups until we reach our start index
            if current_count + len(group) <= start_idx:
                current_count += len(group)
                continue
                
            # Get first and last network in group for context
            first_net = group[0]
            last_net = group[-1]
            
            # Add blank line before group if it's not the first group
            if group_idx > 0 and current_count >= start_idx:
                print()
            
            # Show previous network context for first in group
            prev_net, _ = get_surrounding_networks(first_net, existing_subnets, ipaddress.ip_network(first_net).prefixlen)
            if prev_net and prev_net != last_shown_context:
                prev_subnet = next((s for s in existing_subnets if s['subnet'] == str(prev_net)), None)
                if prev_subnet:
                    site_prefix = "M-" if prev_subnet['source'] == 'meraki' else "Netbox-"
                    print(f"    - {prev_net} ({site_prefix}{prev_subnet['site_name']})")
                    last_shown_context = prev_net
            
            # Show networks in group
            for network in group:
                if current_count >= start_idx and current_count < end_idx:
                    displayed_networks.append(network)
                    print(f"{len(displayed_networks)}   * {network} (available)")
                current_count += 1
                
                # Break if we've shown enough networks for this page
                if current_count >= end_idx:
                    break
            
            # Show next network context for last in group
            if current_count >= start_idx and current_count <= end_idx:
                _, next_net = get_surrounding_networks(last_net, existing_subnets, ipaddress.ip_network(last_net).prefixlen)
                if next_net and next_net != last_shown_context:
                    next_subnet = next((s for s in existing_subnets if s['subnet'] == str(next_net)), None)
                    if next_subnet:
                        site_prefix = "M-" if next_subnet['source'] == 'meraki' else "Netbox-"
                        print(f"    - {next_net} ({site_prefix}{next_subnet['site_name']})")
                        last_shown_context = next_net
            
            # Break if we've shown enough networks for this page
            if current_count >= end_idx:
                break

        print("\nOptions:")
        print(f"1-{len(displayed_networks)} - Select corresponding network")
        if current_page < total_pages - 1:
            print("N - Next page")
        if current_page > 0:
            print("P - Previous page")
        print("X - Exit")
        
        choice = get_input("\nEnter your choice: ").upper()
        
        if choice == 'X':
            print("\nGoodbye!")
            sys.exit(0)  # Exit the program completely
        elif choice == 'N' and current_page < total_pages - 1:
            current_page += 1
        elif choice == 'P' and current_page > 0:
            current_page -= 1
        elif choice.isdigit() and 1 <= int(choice) <= len(displayed_networks):
            return displayed_networks[int(choice) - 1]
        else:
            print("Invalid choice. Please try again.")

def add_network(existing_subnets: List[Dict]):
    """Add a new network workflow"""
    # Get region selection
    region_choice = display_region_menu()
    if region_choice == '5':
        return
    
    # Get network size
    size_choice = display_network_size_menu()
    
    # Get range for selected region
    start_ip, end_ip = get_region_range(region_choice)
    network_size = get_network_size(size_choice)
    
    # Find ALL available networks
    available_networks = find_free_networks(start_ip, end_ip, network_size, existing_subnets)
    
    if not available_networks:
        print("\nNo available networks found in the selected range.")
        return
    
    # Display available networks with context and get selection
    selected_network = display_available_networks(available_networks, existing_subnets, start_ip, end_ip)
    if selected_network == 'exit':
        return
    if selected_network:
        print(f"\nSelected network: {selected_network}")
        nb = setup_netbox()
        post_network_selection_menu(selected_network, nb)

def main():
    print("\nConnecting to Meraki dashboard and Netbox...")
    dashboard = setup_meraki()
    nb = setup_netbox()
    
    print("Gathering subnet information...")
    meraki_subnets = get_organization_subnets(dashboard)
    netbox_subnets = get_netbox_subnets(nb)
    
    combined_subnets = combine_and_sort_subnets(meraki_subnets, netbox_subnets)
    
    if not combined_subnets:
        print("\nNo *********/16 subnets found.")
    else:
        print(f"\nFound {len(combined_subnets)} subnets in use:")
        print(display_subnets_table(combined_subnets))

    add_network(combined_subnets)

if __name__ == "__main__":
    main()
