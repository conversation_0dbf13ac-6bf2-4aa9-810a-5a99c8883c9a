#!/usr/bin/env python3
"""
Script to check if all access points within each UniFi site are on the same network.
Identifies sites where APs are spread across different subnets.
"""

import sys
import ipaddress
import requests
from typing import Dict, List, Set
from getpass import getpass
from tabulate import tabulate

# Configuration
# Configuration
UNIFI_HOST = "https://serv-uni01.limocar.int:8443"
USERNAME = "blos01"
VERIFY_SSL = False  # Set to True if using valid SSL cert

if not VERIFY_SSL:
    requests.packages.urllib3.disable_warnings()

def login_unifi(session: requests.Session, password: str) -> bool:
    """Login to UniFi Controller"""
    login_data = {
        "username": USERNAME,
        "password": password
    }
    
    try:
        resp = session.post(
            f"{UNIFI_HOST}/api/login",
            json=login_data,
            verify=VERIFY_SSL
        )
        return resp.status_code == 200
    except Exception as e:
        print(f"Login error: {e}")
        return False

def get_sites(session: requests.Session) -> List[Dict]:
    """Get all sites from the controller"""
    url = UNIFI_HOST + "/api/self/sites"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    # Fallback for older controllers
    fallback_url = UNIFI_HOST + "/api/stat/sites"
    resp = session.get(fallback_url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        return resp.json().get("data", [])
    
    return []

def get_aps_for_site(session: requests.Session, site_name: str) -> List[Dict]:
    """Get all access points for a specific site"""
    url = f"{UNIFI_HOST}/api/s/{site_name}/stat/device"
    resp = session.get(url, verify=VERIFY_SSL)
    if resp.status_code == 200:
        devices = resp.json().get("data", [])
        # Filter for only APs (type "uap")
        return [dev for dev in devices if dev.get("type") == "uap"]
    return []

def get_network_prefix(ip: str) -> str:
    """Extract network prefix from IP address"""
    try:
        # Assume /24 network if no other information available
        network = ipaddress.ip_network(f"{ip}/24", strict=False)
        return str(network.network_address)
    except ValueError:
        return "Invalid IP"

def get_ap_status(ap: Dict) -> str:
    """Get formatted AP status with uptime if online"""
    state = ap.get("state", 0)
    if state == 1:
        uptime = ap.get("uptime", 0)
        days = uptime // (24 * 3600)
        hours = (uptime % (24 * 3600)) // 3600
        if days > 0:
            return f"Online ({days}d {hours}h)"
        return f"Online ({hours}h)"
    return "Offline"

def analyze_site_networks(aps: List[Dict]) -> Dict:
    """Analyze APs in a site and group them by network"""
    networks: Dict[str, List[Dict]] = {}
    
    for ap in aps:
        ip = ap.get("ip")
        if ip:
            network = get_network_prefix(ip)
            if network not in networks:
                networks[network] = []
            networks[network].append({
                "name": ap.get("name", "Unnamed AP"),
                "model": ap.get("model", "Unknown Model"),
                "ip": ip,
                "mac": ap.get("mac", ""),
                "status": get_ap_status(ap)
            })
    
    return networks

def main():
    print(f"UniFi Controller: {UNIFI_HOST}")
    print(f"Logging in as user: {USERNAME}")
    
    # Get password
    password = getpass("Enter your admin password: ")
    
    # Setup session
    session = requests.Session()
    
    # Login
    if not login_unifi(session, password):
        print("[!] Login failed")
        sys.exit(1)
    
    # Get sites
    sites = get_sites(session)
    if not sites:
        print("[!] No sites found or insufficient permissions")
        sys.exit(0)
    
    # Process each site
    issues_found = False
    for site in sites:
        site_name = site["name"]
        site_desc = site.get("desc", site_name)
        
        print(f"\nAnalyzing site: {site_desc}")
        
        # Get APs for site
        aps = get_aps_for_site(session, site_name)
        if not aps:
            print("  No APs found in this site")
            continue
        
        # Count online/offline APs
        online_count = sum(1 for ap in aps if ap.get("state", 0) == 1)
        total_aps = len(aps)
        print(f"  Total APs: {total_aps} (Online: {online_count}, Offline: {total_aps - online_count})")
        
        # Analyze networks
        networks = analyze_site_networks(aps)
        
        if len(networks) > 1:
            issues_found = True
            print(f"  [!] APs found in {len(networks)} different networks:")
            
            for network, aps_in_net in networks.items():
                print(f"\n  Network: {network}")
                table_data = [
                    [ap["name"], ap["model"], ap["ip"], ap["mac"], ap["status"]]
                    for ap in aps_in_net
                ]
                # Create table and add indentation manually
                table = tabulate(
                    table_data,
                    headers=["AP Name", "Model", "IP Address", "MAC Address", "Status"],
                    tablefmt="simple"
                )
                # Add 4 spaces of indentation to each line
                indented_table = "\n".join("    " + line for line in table.split("\n"))
                print(indented_table)
        else:
            print(f"  [✓] All APs are on the same network")
    
    if not issues_found:
        print("\n[✓] All sites have consistent AP networks")
    else:
        print("\n[!] Some sites have APs on different networks")

if __name__ == "__main__":
    main()
