#!/usr/bin/env python3
"""
Script: meraki_live_wan_analysis.py

Uses LIVE Meraki API calls to get current WAN interface status including:
- Real-time WAN IP addresses (both Static and DHCP-assigned)
- WAN interface configuration (Static vs DHCP)
- Uplink status and connectivity
- Switch inventory
- Cellular gateway presence

This script will capture DHCP-assigned IPs that don't show up in stored device data.
"""

import sys
import json
from typing import Dict, List, Any, NamedTuple, Optional
import meraki
from pathlib import Path
from datetime import datetime
import csv
import time

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

class WANInterface(NamedTuple):
    interface: str  # WAN1, WAN2
    ip_address: str
    connection_type: str  # Static, DHCP, Unknown
    status: str  # Active, Failed, Ready, etc.
    
class SiteWANInfo(NamedTuple):
    site_name: str
    network_id: str
    wan_interfaces: List[WANInterface]
    has_cellular_gateway: bool
    cellular_model: str
    primary_device_model: str
    switch_models: List[str]
    switch_count: int
    notes: str

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def safe_api_call(func, *args, **kwargs) -> Optional[Any]:
    """Safely make API calls with rate limiting and error handling"""
    try:
        result = func(*args, **kwargs)
        # Add small delay to respect rate limits
        time.sleep(0.1)
        return result
    except meraki.APIError as e:
        print(f"    API Error in {func.__name__}: {e}")
        return None
    except Exception as e:
        print(f"    Unexpected Error in {func.__name__}: {e}")
        return None

def get_wan_uplink_status(dashboard: meraki.DashboardAPI, network_id: str) -> List[Dict[str, Any]]:
    """Get real-time WAN uplink status for a network"""
    return safe_api_call(dashboard.appliance.getNetworkApplianceUplinksStatuses, network_id)

def get_wan_uplink_settings(dashboard: meraki.DashboardAPI, serial: str) -> Dict[str, Any]:
    """Get WAN uplink configuration settings for a device"""
    return safe_api_call(dashboard.appliance.getDeviceApplianceUplinksSettings, serial)

def analyze_live_wan_info(dashboard: meraki.DashboardAPI, network_data: Dict[str, Any]) -> SiteWANInfo:
    """Analyze live WAN information for a single site"""
    site_name = network_data['name']
    network_id = network_data['id']
    
    print(f"    Getting devices for: {site_name}")
    devices = safe_api_call(dashboard.networks.getNetworkDevices, network_id)
    
    if not devices:
        print(f"    No devices found for {site_name}")
        return SiteWANInfo(
            site_name=site_name,
            network_id=network_id,
            wan_interfaces=[],
            has_cellular_gateway=False,
            cellular_model="",
            primary_device_model="",
            switch_models=[],
            switch_count=0,
            notes=""
        )
    
    wan_interfaces = []
    has_cellular_gateway = False
    cellular_model = ""
    primary_device_model = ""
    switch_models = []
    combined_notes = ""
    
    # Find appliance devices (MX, Z, MG series)
    appliance_devices = [d for d in devices if d.get('model', '').startswith(('MX', 'Z', 'MG'))]
    
    # Find switch devices (MS series)
    switch_devices = [d for d in devices if d.get('model', '').startswith('MS')]
    
    # Extract switch information
    for switch in switch_devices:
        model = switch.get('model', '')
        if model and model not in switch_models:
            switch_models.append(model)
    
    # Get real-time uplink status
    print(f"    Getting uplink status for: {site_name}")
    uplink_statuses = get_wan_uplink_status(dashboard, network_id)
    
    for device in appliance_devices:
        model = device.get('model', '')
        serial = device.get('serial', '')
        notes = device.get('notes', '')
        combined_notes += f"{notes} " if notes else ""
        
        # Check for cellular gateway
        if model.startswith('MG'):
            has_cellular_gateway = True
            cellular_model = model
        
        # Set primary device model (prefer MX over others)
        if model.startswith('MX') or not primary_device_model:
            primary_device_model = model
        
        # Get uplink configuration settings
        print(f"    Getting uplink settings for device: {device.get('name', serial)}")
        uplink_settings = get_wan_uplink_settings(dashboard, serial)
        
        # Process uplink status information
        if uplink_statuses:
            for uplink_status in uplink_statuses:
                if uplink_status.get('serial') == serial:
                    uplinks = uplink_status.get('uplinks', [])
                    
                    for uplink in uplinks:
                        interface_name = uplink.get('interface', 'Unknown')
                        current_ip = uplink.get('publicIp', 'No IP')
                        status = uplink.get('status', 'Unknown')
                        
                        # Determine connection type from settings
                        connection_type = "Unknown"
                        if uplink_settings and 'wan1' in uplink_settings:
                            wan_config = uplink_settings.get('wan1', {})
                            if interface_name.upper() == 'WAN1':
                                wan_type = wan_config.get('wanEnabled', 'Unknown')
                                if wan_type == 'enabled':
                                    using_static_ip = wan_config.get('usingStaticIp', False)
                                    connection_type = "Static" if using_static_ip else "DHCP"
                        
                        if uplink_settings and 'wan2' in uplink_settings:
                            wan_config = uplink_settings.get('wan2', {})
                            if interface_name.upper() == 'WAN2':
                                wan_type = wan_config.get('wanEnabled', 'Unknown')
                                if wan_type == 'enabled':
                                    using_static_ip = wan_config.get('usingStaticIp', False)
                                    connection_type = "Static" if using_static_ip else "DHCP"
                        
                        # Only add interfaces that are active or have IPs
                        if current_ip != 'No IP' or status in ['active', 'ready']:
                            wan_interfaces.append(WANInterface(
                                interface=interface_name.upper(),
                                ip_address=current_ip,
                                connection_type=connection_type,
                                status=status
                            ))
        
        # Fallback: Check stored device data for static IPs
        wan1_ip = device.get('wan1Ip')
        wan2_ip = device.get('wan2Ip')
        
        # Add WAN1 if found in device data but not in uplink status
        if wan1_ip and not any(w.interface == 'WAN1' for w in wan_interfaces):
            connection_type = determine_connection_type_from_notes(wan1_ip, notes)
            wan_interfaces.append(WANInterface(
                interface="WAN1",
                ip_address=wan1_ip,
                connection_type=connection_type,
                status="Active (from device data)"
            ))
        
        # Add WAN2 if found in device data but not in uplink status
        if wan2_ip and not any(w.interface == 'WAN2' for w in wan_interfaces):
            connection_type = determine_connection_type_from_notes(wan2_ip, notes)
            wan_interfaces.append(WANInterface(
                interface="WAN2",
                ip_address=wan2_ip,
                connection_type=connection_type,
                status="Active (from device data)"
            ))
    
    return SiteWANInfo(
        site_name=site_name,
        network_id=network_id,
        wan_interfaces=wan_interfaces,
        has_cellular_gateway=has_cellular_gateway,
        cellular_model=cellular_model,
        primary_device_model=primary_device_model,
        switch_models=switch_models,
        switch_count=len(switch_devices),
        notes=combined_notes.strip()
    )

def determine_connection_type_from_notes(wan_ip: str, notes: str) -> str:
    """Determine connection type from notes (fallback method)"""
    if not wan_ip:
        return "No IP"
    
    notes_lower = notes.lower() if notes else ""
    
    if "pppoe" in notes_lower or "dhcp" in notes_lower:
        return "DHCP/PPPoE"
    elif "static" in notes_lower:
        return "Static"
    else:
        return "Static"  # Default assumption for stored IPs

def analyze_all_sites_live(dashboard: meraki.DashboardAPI) -> List[SiteWANInfo]:
    """Analyze live WAN information for all sites"""
    all_sites_wan = []
    
    print("Getting organizations...")
    orgs = safe_api_call(dashboard.organizations.getOrganizations)
    if not orgs:
        print("No organizations found")
        return all_sites_wan
    
    # Use first organization
    org = orgs[0]
    org_id = org['id']
    print(f"Using organization: {org['name']}")
    
    print("Getting networks...")
    networks = safe_api_call(dashboard.organizations.getOrganizationNetworks, org_id)
    if not networks:
        print("No networks found")
        return all_sites_wan
    
    print(f"Found {len(networks)} networks. Analyzing WAN configurations...")
    
    for i, network in enumerate(networks, 1):
        site_name = network['name']
        print(f"  [{i}/{len(networks)}] Processing: {site_name}")
        
        try:
            site_wan_info = analyze_live_wan_info(dashboard, network)
            all_sites_wan.append(site_wan_info)
        except Exception as e:
            print(f"    Error processing {site_name}: {e}")
            # Add placeholder entry for failed sites
            all_sites_wan.append(SiteWANInfo(
                site_name=site_name,
                network_id=network['id'],
                wan_interfaces=[],
                has_cellular_gateway=False,
                cellular_model="",
                primary_device_model="Error",
                switch_models=[],
                switch_count=0,
                notes=f"Error: {e}"
            ))
    
    return all_sites_wan

def create_live_wan_report(sites_wan: List[SiteWANInfo]) -> str:
    """Create a formatted live WAN interface report"""
    report = []
    report.append("MERAKI LIVE WAN INTERFACE ANALYSIS")
    report.append("=" * 60)
    report.append("")
    report.append(f"Analysis performed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"Total sites analyzed: {len(sites_wan)}")
    report.append("")
    
    # Summary statistics
    sites_with_wan1 = len([s for s in sites_wan if any(w.interface == "WAN1" for w in s.wan_interfaces)])
    sites_with_wan2 = len([s for s in sites_wan if any(w.interface == "WAN2" for w in s.wan_interfaces)])
    sites_with_cellular = len([s for s in sites_wan if s.has_cellular_gateway])
    sites_with_switches = len([s for s in sites_wan if s.switch_count > 0])
    sites_with_static = len([s for s in sites_wan if any("Static" in w.connection_type for w in s.wan_interfaces)])
    sites_with_dhcp = len([s for s in sites_wan if any("DHCP" in w.connection_type for w in s.wan_interfaces)])
    total_switches = sum(s.switch_count for s in sites_wan)
    
    report.append("SUMMARY STATISTICS:")
    report.append("-" * 20)
    report.append(f"Sites with WAN1 interface: {sites_with_wan1}")
    report.append(f"Sites with WAN2 interface: {sites_with_wan2}")
    report.append(f"Sites with Cellular Gateway: {sites_with_cellular}")
    report.append(f"Sites with Switches: {sites_with_switches}")
    report.append(f"Total Switches across all sites: {total_switches}")
    report.append(f"Sites with Static IP: {sites_with_static}")
    report.append(f"Sites with DHCP/Dynamic IP: {sites_with_dhcp}")
    report.append("")
    
    # Detailed site information
    report.append("DETAILED SITE WAN INFORMATION:")
    report.append("-" * 35)
    report.append("")
    
    for site in sites_wan:
        report.append(f"Site: {site.site_name}")
        report.append(f"Primary Device: {site.primary_device_model}")
        
        if site.has_cellular_gateway:
            report.append(f"Cellular Gateway: YES ({site.cellular_model})")
        else:
            report.append("Cellular Gateway: NO")
        
        # Switch information
        if site.switch_count > 0:
            switch_info = f"Switches: {site.switch_count} ({', '.join(site.switch_models)})"
            report.append(switch_info)
        else:
            report.append("Switches: None")
        
        if site.wan_interfaces:
            report.append("WAN Interfaces:")
            for wan in site.wan_interfaces:
                report.append(f"  {wan.interface}: {wan.ip_address} ({wan.connection_type}) - {wan.status}")
        else:
            report.append("WAN Interfaces: None configured")
        
        # Extract ISP info from notes if available
        if site.notes:
            notes_lines = site.notes.replace('\n', ' | ').strip()
            if len(notes_lines) > 100:
                notes_lines = notes_lines[:100] + "..."
            report.append(f"Notes: {notes_lines}")
        
        report.append("")
    
    return "\n".join(report)

def save_live_wan_data_csv(sites_wan: List[SiteWANInfo]) -> Path:
    """Save live WAN data to CSV file"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_file = Path(f'meraki_live_wan_interfaces_{timestamp}.csv')
    
    with csv_file.open('w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # Header
        writer.writerow([
            'Site Name',
            'Network ID', 
            'Primary Device Model',
            'WAN1 IP',
            'WAN1 Type',
            'WAN1 Status',
            'WAN2 IP', 
            'WAN2 Type',
            'WAN2 Status',
            'Has Cellular Gateway',
            'Cellular Model',
            'Switch Count',
            'Switch Models',
            'Total WAN Interfaces',
            'ISP Notes'
        ])
        
        # Data rows
        for site in sites_wan:
            wan1_ip = ""
            wan1_type = ""
            wan1_status = ""
            wan2_ip = ""
            wan2_type = ""
            wan2_status = ""
            
            for wan in site.wan_interfaces:
                if wan.interface == "WAN1":
                    wan1_ip = wan.ip_address
                    wan1_type = wan.connection_type
                    wan1_status = wan.status
                elif wan.interface == "WAN2":
                    wan2_ip = wan.ip_address
                    wan2_type = wan.connection_type
                    wan2_status = wan.status
            
            # Clean notes for CSV
            clean_notes = site.notes.replace('\n', ' | ').replace('\r', '') if site.notes else ""
            switch_models_str = ', '.join(site.switch_models) if site.switch_models else ""
            
            writer.writerow([
                site.site_name,
                site.network_id,
                site.primary_device_model,
                wan1_ip,
                wan1_type,
                wan1_status,
                wan2_ip,
                wan2_type,
                wan2_status,
                "YES" if site.has_cellular_gateway else "NO",
                site.cellular_model,
                site.switch_count,
                switch_models_str,
                len(site.wan_interfaces),
                clean_notes
            ])
    
    return csv_file

def main():
    print("MERAKI LIVE WAN INTERFACE ANALYSIS")
    print("=" * 40)
    print("This script uses LIVE API calls to get current WAN interface status")
    print("including DHCP-assigned IP addresses that don't appear in stored data.")
    print()
    
    print("Connecting to Meraki dashboard...")
    dashboard = setup_meraki()
    
    print("Collecting live WAN interface data for all sites...")
    print("Note: This may take several minutes due to API rate limits.")
    print()
    
    sites_wan = analyze_all_sites_live(dashboard)
    
    if not sites_wan:
        print("No sites found for analysis.")
        return
    
    # Create report
    print("Generating live WAN interface report...")
    report = create_live_wan_report(sites_wan)
    
    # Save files
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save text report
    report_file = Path(f'meraki_live_wan_analysis_{timestamp}.txt')
    with report_file.open('w', encoding='utf-8') as f:
        f.write(report)
    
    # Save CSV data
    csv_file = save_live_wan_data_csv(sites_wan)
    
    # Display summary
    print("\n" + "="*60)
    print("LIVE WAN INTERFACE ANALYSIS COMPLETE")
    print("="*60)
    print(f"Sites analyzed: {len(sites_wan)}")
    print(f"Text report saved to: {report_file}")
    print(f"CSV data saved to: {csv_file}")
    print()
    print("This analysis includes real-time DHCP-assigned IP addresses")
    print("that were missing from the previous historical data analysis.")
    print()
    print("Check the BC-CHILLIWACK site - it should now show the correct")
    print("WAN1 IP address: ************** (DHCP)")

if __name__ == "__main__":
    main()
