#!/usr/bin/env python3
"""
Script: test_chilliwack_wan.py

Quick test to get WAN status for BC-CHILLIWACK site specifically
to verify the DHCP IP address issue.
"""

import meraki
import json
from datetime import datetime

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

def test_chilliwack_wan():
    """Test WAN status for CHILLIWACK site"""
    print("Testing BC-CHILLIWACK WAN interface...")
    
    try:
        dashboard = meraki.DashboardAPI(
            api_key=MERAKI_API_KEY,
            output_log=False,
            print_console=False,
            suppress_logging=True
        )
        
        print("Getting organizations...")
        orgs = dashboard.organizations.getOrganizations()
        
        if not orgs:
            print("No organizations found")
            return
        
        org_id = orgs[0]['id']
        print(f"Using organization: {orgs[0]['name']}")
        
        print("Getting networks...")
        networks = dashboard.organizations.getOrganizationNetworks(org_id)
        
        # Find CHILLIWACK network
        chilliwack_network = None
        for network in networks:
            if "CHILLIWACK" in network['name'].upper():
                chilliwack_network = network
                break
        
        if not chilliwack_network:
            print("CHILLIWACK network not found")
            return
        
        network_id = chilliwack_network['id']
        site_name = chilliwack_network['name']
        print(f"Found CHILLIWACK site: {site_name}")
        print(f"Network ID: {network_id}")
        
        print("Getting devices...")
        devices = dashboard.networks.getNetworkDevices(network_id)
        
        if not devices:
            print("No devices found")
            return
        
        print(f"Found {len(devices)} devices:")
        for device in devices:
            print(f"  - {device.get('name', 'Unnamed')}: {device.get('model', 'Unknown')} ({device.get('serial', 'N/A')})")
            print(f"    WAN1 IP (stored): {device.get('wan1Ip', 'None')}")
            print(f"    WAN2 IP (stored): {device.get('wan2Ip', 'None')}")
        
        # Get uplink status
        print("\nGetting uplink status...")
        try:
            uplink_statuses = dashboard.appliance.getNetworkApplianceUplinksStatuses(network_id)
            
            if uplink_statuses:
                print("Uplink status found:")
                for status in uplink_statuses:
                    print(f"  Device Serial: {status.get('serial', 'Unknown')}")
                    uplinks = status.get('uplinks', [])
                    for uplink in uplinks:
                        interface = uplink.get('interface', 'Unknown')
                        public_ip = uplink.get('publicIp', 'No IP')
                        status_text = uplink.get('status', 'Unknown')
                        print(f"    {interface}: {public_ip} (Status: {status_text})")
            else:
                print("No uplink status data found")
        
        except Exception as e:
            print(f"Error getting uplink status: {e}")
        
        # Get uplink settings for MX devices
        mx_devices = [d for d in devices if d.get('model', '').startswith('MX')]
        
        for device in mx_devices:
            serial = device.get('serial', '')
            print(f"\nGetting uplink settings for {device.get('name', serial)}...")
            
            try:
                uplink_settings = dashboard.appliance.getDeviceApplianceUplinksSettings(serial)
                
                if uplink_settings:
                    print("Uplink settings:")
                    
                    wan1 = uplink_settings.get('wan1', {})
                    if wan1:
                        enabled = wan1.get('wanEnabled', 'Unknown')
                        using_static = wan1.get('usingStaticIp', False)
                        connection_type = "Static" if using_static else "DHCP"
                        print(f"  WAN1: Enabled={enabled}, Type={connection_type}")
                        
                        if using_static:
                            static_ip = wan1.get('staticIp', 'Not set')
                            print(f"    Static IP: {static_ip}")
                    
                    wan2 = uplink_settings.get('wan2', {})
                    if wan2:
                        enabled = wan2.get('wanEnabled', 'Unknown')
                        using_static = wan2.get('usingStaticIp', False)
                        connection_type = "Static" if using_static else "DHCP"
                        print(f"  WAN2: Enabled={enabled}, Type={connection_type}")
                        
                        if using_static:
                            static_ip = wan2.get('staticIp', 'Not set')
                            print(f"    Static IP: {static_ip}")
                else:
                    print("No uplink settings found")
            
            except Exception as e:
                print(f"Error getting uplink settings: {e}")
        
        print(f"\nTest completed for {site_name}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_chilliwack_wan()
