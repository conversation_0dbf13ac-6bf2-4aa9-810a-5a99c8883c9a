#!/usr/bin/env python3
"""
Debug script to check WAN configuration for BC_Kelowna site
"""

import meraki
import json

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

def setup_meraki():
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def main():
    dashboard = setup_meraki()
    
    print("Looking for BC_Kelowna network...")
    
    # Get all organizations
    orgs = dashboard.organizations.getOrganizations()
    
    for org in orgs:
        org_id = org['id']
        org_name = org['name']
        print(f"Checking organization: {org_name}")
        
        # Get all networks
        networks = dashboard.organizations.getOrganizationNetworks(org_id)
        
        for network in networks:
            network_name = network['name']
            
            if "Kelowna" in network_name:
                print(f"\nFound network: {network_name}")
                network_id = network['id']
                
                # Get devices
                devices = dashboard.networks.getNetworkDevices(network_id)
                
                for device in devices:
                    if device.get('model', '').startswith('MX'):
                        device_serial = device['serial']
                        print(f"Found MX device: {device.get('name', 'Unknown')} ({device_serial})")
                        
                        # Try to get uplink settings
                        try:
                            uplink_settings = dashboard.appliance.getDeviceApplianceUplinksSettings(device_serial)
                            print(f"Uplink settings: {json.dumps(uplink_settings, indent=2)}")
                        except Exception as e:
                            print(f"Error getting uplink settings: {e}")
                        
                        # Try organization uplinks status
                        try:
                            uplinks_status = dashboard.organizations.getOrganizationUplinksStatuses(org_id, serials=[device_serial])
                            print(f"Organization uplinks status: {json.dumps(uplinks_status, indent=2)}")
                        except Exception as e:
                            print(f"Error getting organization uplinks status: {e}")
                        
                        return  # Found what we're looking for

if __name__ == "__main__":
    main()
