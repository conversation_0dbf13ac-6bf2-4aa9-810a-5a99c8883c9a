#!/usr/bin/env python3
"""
Script: meraki_comprehensive_site_details.py

Extracts EVERY possible detail for the first site found in Meraki dashboard including:
- Basic site information (name, address, notes, tags)
- Network configuration details
- All devices and their configurations
- VLANs and subnets
- Security policies
- Wireless settings
- Appliance settings
- Switch settings
- And much more...

Requirements:
    pip install meraki tabulate
"""

import sys
import json
from typing import Dict, List, Any
import meraki
from tabulate import tabulate
from pathlib import Path
from datetime import datetime

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def safe_api_call(func, *args, **kwargs):
    """Safely make API calls and handle errors"""
    try:
        return func(*args, **kwargs)
    except meraki.APIError as e:
        print(f"    API Error: {e}")
        return None
    except Exception as e:
        print(f"    Unexpected Error: {e}")
        return None

def get_first_site_details(dashboard: meraki.DashboardAPI) -> Dict[str, Any]:
    """Get comprehensive details for the first site found"""
    site_details = {}

    try:
        print("Getting organizations...")
        # Get organizations
        orgs = dashboard.organizations.getOrganizations()
        if not orgs:
            print("No organizations found")
            return site_details

        # Use first organization
        org = orgs[0]
        site_details['organization'] = org
        print(f"Using organization: {org['name']}")

        print("Getting networks...")
        # Get networks
        networks = dashboard.organizations.getOrganizationNetworks(org['id'])
        if not networks:
            print("No networks found")
            return site_details

        print(f"Found {len(networks)} networks")

        # Use first network as our "site" - specifically "QC-Quebec - 2680 boul. Wilfrid-Hamel"
        network = networks[0]
        site_details['network'] = network
        print(f"Analyzing site: {network['name']}")

        network_id = network['id']
        org_id = org['id']

        # === BASIC NETWORK INFORMATION ===
        print("  Getting network details...")
        site_details['network_details'] = safe_api_call(
            dashboard.networks.getNetwork, network_id
        )

        # === DEVICES ===
        print("  Getting devices...")
        devices = safe_api_call(dashboard.networks.getNetworkDevices, network_id)
        site_details['devices'] = devices

        # Get detailed device information
        if devices:
            site_details['device_details'] = {}
            for device in devices:
                serial = device['serial']
                print(f"    Getting details for device: {device.get('name', serial)}")

                # Device status
                device_status = safe_api_call(
                    dashboard.devices.getDevice, serial
                )

                # Device uplink info
                device_uplink = safe_api_call(
                    dashboard.devices.getDeviceUplink, serial
                )

                site_details['device_details'][serial] = {
                    'basic_info': device,
                    'status': device_status,
                    'uplink': device_uplink
                }

        # === APPLIANCE SETTINGS ===
        print("  Getting appliance settings...")

        # VLANs
        site_details['vlans'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceVlans, network_id
        )

        # Firewall rules
        site_details['firewall_l3_rules'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceFirewallL3FirewallRules, network_id
        )

        site_details['firewall_l7_rules'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceFirewallL7FirewallRules, network_id
        )

        # Port forwarding rules
        site_details['port_forwarding'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceFirewallPortForwardingRules, network_id
        )

        # Site-to-site VPN
        site_details['site_to_site_vpn'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceVpnSiteToSiteVpn, network_id
        )

        # Content filtering
        site_details['content_filtering'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceContentFiltering, network_id
        )

        # Traffic shaping
        site_details['traffic_shaping'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceTrafficShaping, network_id
        )

        # Security intrusion
        site_details['security_intrusion'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceSecurityIntrusion, network_id
        )

        # Connectivity monitoring destinations
        site_details['connectivity_monitoring'] = safe_api_call(
            dashboard.appliance.getNetworkApplianceConnectivityMonitoringDestinations, network_id
        )

        # === WIRELESS SETTINGS ===
        print("  Getting wireless settings...")

        # SSIDs
        site_details['ssids'] = safe_api_call(
            dashboard.wireless.getNetworkWirelessSsids, network_id
        )

        # RF profiles
        site_details['rf_profiles'] = safe_api_call(
            dashboard.wireless.getNetworkWirelessRfProfiles, network_id
        )

        # Wireless settings
        site_details['wireless_settings'] = safe_api_call(
            dashboard.wireless.getNetworkWirelessSettings, network_id
        )

        # === SWITCH SETTINGS ===
        print("  Getting switch settings...")

        # Switch ports
        site_details['switch_ports'] = {}
        if devices:
            for device in devices:
                if device['model'].startswith('MS'):  # Meraki switches
                    serial = device['serial']
                    ports = safe_api_call(
                        dashboard.switch.getDeviceSwitchPorts, serial
                    )
                    if ports:
                        site_details['switch_ports'][serial] = ports

        # Switch settings
        site_details['switch_settings'] = safe_api_call(
            dashboard.switch.getNetworkSwitchSettings, network_id
        )

        # === ORGANIZATION LEVEL SETTINGS ===
        print("  Getting organization settings...")

        # Organization details
        site_details['org_details'] = safe_api_call(
            dashboard.organizations.getOrganization, org_id
        )

        # SAML roles
        site_details['saml_roles'] = safe_api_call(
            dashboard.organizations.getOrganizationSamlRoles, org_id
        )

        # Admins
        site_details['admins'] = safe_api_call(
            dashboard.organizations.getOrganizationAdmins, org_id
        )

        # === ADDITIONAL NETWORK SETTINGS ===
        print("  Getting additional network settings...")

        # Network settings
        site_details['network_settings'] = safe_api_call(
            dashboard.networks.getNetworkSettings, network_id
        )

        # Network traffic
        site_details['network_traffic'] = safe_api_call(
            dashboard.networks.getNetworkTraffic, network_id
        )

        # Network events
        site_details['network_events'] = safe_api_call(
            dashboard.networks.getNetworkEvents, network_id
        )

        # Network clients
        site_details['network_clients'] = safe_api_call(
            dashboard.networks.getNetworkClients, network_id
        )

        print(f"  Comprehensive data collection complete for: {network['name']}")

    except Exception as e:
        print(f"Error collecting site details: {e}")

    return site_details

def save_comprehensive_report(site_details: Dict[str, Any]) -> Path:
    """Save comprehensive site details to JSON file"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Save raw JSON data
    json_file = Path(f'meraki_comprehensive_site_details_{timestamp}.json')
    with json_file.open('w', encoding='utf-8') as f:
        json.dump(site_details, f, indent=2, default=str)

    # Create human-readable summary
    summary_file = Path(f'meraki_site_summary_{timestamp}.txt')
    with summary_file.open('w', encoding='utf-8') as f:
        f.write("MERAKI COMPREHENSIVE SITE DETAILS SUMMARY\n")
        f.write("=" * 50 + "\n\n")

        if 'network' in site_details:
            net = site_details['network']
            f.write(f"Site Name: {net.get('name', 'N/A')}\n")
            f.write(f"Network ID: {net.get('id', 'N/A')}\n")
            f.write(f"Organization: {site_details.get('organization', {}).get('name', 'N/A')}\n")
            f.write(f"Address: {net.get('address', 'N/A')}\n")
            f.write(f"Notes: {net.get('notes', 'N/A')}\n")
            f.write(f"Tags: {', '.join(net.get('tags', []))}\n\n")

        # Device summary
        if 'devices' in site_details and site_details['devices']:
            f.write("DEVICES:\n")
            f.write("-" * 20 + "\n")
            for device in site_details['devices']:
                f.write(f"  {device.get('name', 'Unnamed')} ({device.get('model', 'Unknown')}) - {device.get('serial', 'N/A')}\n")
            f.write("\n")

        # VLAN summary
        if 'vlans' in site_details and site_details['vlans']:
            f.write("VLANs:\n")
            f.write("-" * 20 + "\n")
            for vlan in site_details['vlans']:
                f.write(f"  VLAN {vlan.get('id', 'N/A')}: {vlan.get('name', 'Unnamed')} - {vlan.get('subnet', 'N/A')}\n")
            f.write("\n")

        # SSID summary
        if 'ssids' in site_details and site_details['ssids']:
            f.write("WIRELESS SSIDs:\n")
            f.write("-" * 20 + "\n")
            for ssid in site_details['ssids']:
                if ssid.get('enabled', False):
                    f.write(f"  {ssid.get('name', 'Unnamed')} (VLAN {ssid.get('defaultVlanId', 'N/A')})\n")
            f.write("\n")

        f.write(f"\nFull details saved to: {json_file}\n")
        f.write(f"Data sections collected: {len([k for k, v in site_details.items() if v is not None])}\n")

    return json_file, summary_file

def main():
    print("\nConnecting to Meraki dashboard...")
    dashboard = setup_meraki()

    print("Collecting comprehensive details for first site...")
    site_details = get_first_site_details(dashboard)

    if not site_details:
        print("No site details collected.")
        return

    # Save comprehensive report
    json_file, summary_file = save_comprehensive_report(site_details)

    print(f"\n[+] Comprehensive data saved to: {json_file}")
    print(f"[+] Summary saved to: {summary_file}")
    print(f"\nData sections collected: {len([k for k, v in site_details.items() if v is not None])}")

    # Display what was collected
    print("\nData sections collected:")
    for key, value in site_details.items():
        if value is not None:
            if isinstance(value, list):
                print(f"  {key}: {len(value)} items")
            elif isinstance(value, dict):
                print(f"  {key}: {len(value)} keys")
            else:
                print(f"  {key}: collected")

if __name__ == "__main__":
    main()
