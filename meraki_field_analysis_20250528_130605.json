{"organizations": {"580401401977375879": {"name": "MPaaS-Transdev Canada Inc", "fields": ["org.api", "org.api.enabled", "org.cloud", "org.cloud.region", "org.cloud.region.host", "org.cloud.region.host.name", "org.cloud.region.name", "org.id", "org.licensing", "org.licensing.model", "org.management", "org.management.details", "org.management.details[0].name", "org.management.details[0].value", "org.name", "org.samlConsumerUrl", "org.samlConsumerUrls", "org.url"]}}, "networks": {}, "devices": {"L_3850014731448352773": {"network_name": "QC-Quebec - 2680 boul. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_3850014731448352782": {"network_name": "ON-Brampton-7324 Kennedy Rd South", "device_count": 4, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_3850014731448352833": {"network_name": "AB-Calgary - 1119 46 Ave SE", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_3850014731448352836": {"network_name": "QC-Chambly-940 Av. simard", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_3850014731448352838": {"network_name": "ON-Etobicoke - 21 Steinway Blvd", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_3850014731448352973": {"network_name": "AB-Airdrie - 90 Highland Park", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_3850014731448352978": {"network_name": "ON-Welland - 1644 Merrittville", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_3850014731448353014": {"network_name": "QC-Brossard-1040 Rue du Lux-suite 510", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_3850014731448353020": {"network_name": "QC-Boucherville-701 des Ateliers", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_3850014731448353071": {"network_name": "SK-Regina - 900 9th Ave", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_3850014731448353192": {"network_name": "BC-Salomon Arm - 481 7th St.", "device_count": 3, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_3850014731448353193": {"network_name": "BC-Surrey-17780 56 Ave", "device_count": 3, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.serial", "device.tags", "device.url"]}, "L_3850014731448353194": {"network_name": " BC-DC3-Vancouver-1050 <PERSON><PERSON>", "device_count": 4, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_3850014731448353196": {"network_name": "BC-Vernon - 2401_<PERSON><PERSON>", "device_count": 4, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390565": {"network_name": " QC-DC1_Boucherville-1350Nobel-<PERSON><PERSON><PERSON>", "device_count": 3, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_580401401977390574": {"network_name": "QC-EastAngus-65<PERSON>illard", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390934": {"network_name": "QC-<PERSON><PERSON>rook-1075Talbot", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390938": {"network_name": "QC-Sherbrook-60KingO", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390941": {"network_name": "QC-Beloeil-1500Louis-Marchand", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390942": {"network_name": "QC-Chateauguay-210BlvdIndustriel", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390943": {"network_name": "QC-Boucherville-220J.-A.<PERSON>ardier", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390972": {"network_name": "QC-Montreal-1717<PERSON><PERSON><PERSON>", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390975": {"network_name": "QC-St-Jean-Richelieu-720 Trotter", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977390976": {"network_name": "AB-Fort McKay - Barge Landing Road", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391043": {"network_name": "ON-LondonSOV1-430 Sovereign", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391045": {"network_name": "ON-London-573 Admiral", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_580401401977391046": {"network_name": "ON-Woodstock-1-925 Devonshire", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391048": {"network_name": "ON-Mississauga-110 <PERSON><PERSON>", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391053": {"network_name": "ON-Carp-100 Cardevco Rd", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391054": {"network_name": "ON-Chatham-80 Barthe Street", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391055": {"network_name": "ON-StThomas-614 Talbot St", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391139": {"network_name": "ON-Owen<PERSON>ound-104-<PERSON> room4A", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391140": {"network_name": "ON-StoneyCreek-486 <PERSON><PERSON>win <PERSON>", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391142": {"network_name": "ON-Newmarket-122 BalesDr", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391143": {"network_name": "ON-Etobocike-12 Lockport Ave", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391145": {"network_name": "ON-Kitchener-599 Wabanaki Dr", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391147": {"network_name": "ON-Tillsonburg-60 Cedar St", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391148": {"network_name": "ON-Lively-129 Fielding Rd", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391149": {"network_name": "ON-NiagaraFalls-7017 Oakwood Dr", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391150": {"network_name": "ON-Gormley-23 <PERSON><PERSON> Dr", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977391151": {"network_name": "ON-Toronto-2 Queen St East", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977396625": {"network_name": "ON-Windsor-2679 Howard <PERSON>", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977396626": {"network_name": "ON-Kitchener- 221 <PERSON> Dr", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977396666": {"network_name": "QC-VaudreuilDorion-305 Bd Cite Des Jeunes", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977396763": {"network_name": "ON-Simcoe-40 Park RD", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977396775": {"network_name": "ON-Durham-15 Hardwood AveS", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977397468": {"network_name": " ON-DC2-LondonGRV-970GreenValley", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977397626": {"network_name": "ON-Kingston-626 Cataraqui Woods Dr", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977397627": {"network_name": "ON-Nepean-11 Bentley Ave", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977398304": {"network_name": "ON-OwenSound-1020 3rdAveE", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977398306": {"network_name": "ON-Astorville-49 BelecqueRd", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977398307": {"network_name": "ON-Hanover-301205 Knapville Rd", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977400161": {"network_name": "SK-Regina-140 4thAveEast", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_580401401977400633": {"network_name": "QC-Repentigny-1500 Rue Raymond", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977400996": {"network_name": "AB-Fort McKay-42_Canterra road", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977401494": {"network_name": "QC-Boisbriand-4243Marcel-Lacasse", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977401554": {"network_name": "AB-Calgary-350 7 Ave SW #2400", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_580401401977403064": {"network_name": "QC-Beloeil-1400Louis-Marchand", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977403540": {"network_name": "ON-New Liskeard- 41 Golding St", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977403541": {"network_name": "MB-Winnipeg- 1103 Pacific Ave", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_580401401977403542": {"network_name": "ON-Bradford-140 Holland St W", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977404934": {"network_name": "QC-<PERSON><PERSON> - 332 Ch. <PERSON>-<PERSON>Xavier", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405670": {"network_name": "BC-Kelowna - 1494 Hardy Street", "device_count": 3, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url"]}, "L_580401401977405671": {"network_name": "BC-Surrey - 17535 55B AVE SURREY", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405725": {"network_name": "BC-Abbotsford - 3032 <PERSON>", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405726": {"network_name": "BC-Duncan - 5271 Boal Rd", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_580401401977405766": {"network_name": "BC-Burnaby-3267 Norland Ave", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405768": {"network_name": "BC-Vernon - 2400_43 St", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_580401401977405770": {"network_name": "BC-Richmond - 9871 RIVER DR", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405772": {"network_name": "BC-Maple Ridge -11469 KINGSTON ST", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405774": {"network_name": "BC-Kamloops - 1460 Ord Rd", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405775": {"network_name": "BC-Vancouver - 1465 THORNTON ST", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405776": {"network_name": "BC-Vancouver - 2625 SKEENA ST", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405777": {"network_name": "BC-South Kitimat - 780 Lahakas Blvd", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_580401401977405778": {"network_name": "BC-CHILLIWACK - 44275 Yale Rd West", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977405780": {"network_name": "NT-Yellowknife - 107 Kam Lake Road", "device_count": 2, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url"]}, "L_580401401977407357": {"network_name": "QC-Chateauguay - 2825 Bl. Ford", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "L_580401401977407732": {"network_name": "ON-London-4183 Blakie Rd", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}, "N_3850014731448353124": {"network_name": "ON-LondonSOV2-535 Sovereign", "device_count": 1, "sample_device_fields": ["device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip"]}}, "vlans": {"L_3850014731448352773": {"network_name": "QC-Quebec - 2680 boul. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448352782": {"network_name": "ON-Brampton-7324 Kennedy Rd South", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_3850014731448352833": {"network_name": "AB-Calgary - 1119 46 Ave SE", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448352836": {"network_name": "QC-Chambly-940 Av. simard", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448352838": {"network_name": "ON-Etobicoke - 21 Steinway Blvd", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_3850014731448352973": {"network_name": "AB-Airdrie - 90 Highland Park", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448352978": {"network_name": "ON-Welland - 1644 Merrittville", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448353014": {"network_name": "QC-Brossard-1040 Rue du Lux-suite 510", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448353020": {"network_name": "QC-Boucherville-701 des Ateliers", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448353071": {"network_name": "SK-Regina - 900 9th Ave", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448353192": {"network_name": "BC-Salomon Arm - 481 7th St.", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448353193": {"network_name": "BC-Surrey-17780 56 Ave", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_3850014731448353196": {"network_name": "BC-Vernon - 2401_<PERSON><PERSON>", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977390565": {"network_name": " QC-DC1_Boucherville-1350Nobel-<PERSON><PERSON><PERSON>", "vlan_count": 1, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpHandling", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977390574": {"network_name": "QC-EastAngus-65<PERSON>illard", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977390934": {"network_name": "QC-<PERSON><PERSON>rook-1075Talbot", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977390938": {"network_name": "QC-Sherbrook-60KingO", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977390941": {"network_name": "QC-Beloeil-1500Louis-Marchand", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977390942": {"network_name": "QC-Chateauguay-210BlvdIndustriel", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977390943": {"network_name": "QC-Boucherville-220J.-A.<PERSON>ardier", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977390972": {"network_name": "QC-Montreal-1717<PERSON><PERSON><PERSON>", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977390975": {"network_name": "QC-St-Jean-Richelieu-720 Trotter", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977390976": {"network_name": "AB-Fort McKay - Barge Landing Road", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977391043": {"network_name": "ON-LondonSOV1-430 Sovereign", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391045": {"network_name": "ON-London-573 Admiral", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391046": {"network_name": "ON-Woodstock-1-925 Devonshire", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391048": {"network_name": "ON-Mississauga-110 <PERSON><PERSON>", "vlan_count": 3, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.fixedIpAssignments.18:e8:29:08:66:05", "vlan.fixedIpAssignments.18:e8:29:08:66:05.ip", "vlan.fixedIpAssignments.18:e8:29:08:66:05.name", "vlan.fixedIpAssignments.18:e8:29:08:66:28", "vlan.fixedIpAssignments.18:e8:29:08:66:28.ip", "vlan.fixedIpAssignments.18:e8:29:08:66:28.name", "vlan.fixedIpAssignments.e0:63:da:a9:ed:da", "vlan.fixedIpAssignments.e0:63:da:a9:ed:da.ip", "vlan.fixedIpAssignments.e0:63:da:a9:ed:da.name", "vlan.fixedIpAssignments.e0:63:da:a9:ef:da", "vlan.fixedIpAssignments.e0:63:da:a9:ef:da.ip", "vlan.fixedIpAssignments.e0:63:da:a9:ef:da.name", "vlan.fixedIpAssignments.e0:63:da:a9:f1:fc", "vlan.fixedIpAssignments.e0:63:da:a9:f1:fc.ip", "vlan.fixedIpAssignments.e0:63:da:a9:f1:fc.name", "vlan.fixedIpAssignments.e0:63:da:a9:f4:e1", "vlan.fixedIpAssignments.e0:63:da:a9:f4:e1.ip", "vlan.fixedIpAssignments.e0:63:da:a9:f4:e1.name", "vlan.fixedIpAssignments.e0:63:da:a9:f6:ec", "vlan.fixedIpAssignments.e0:63:da:a9:f6:ec.ip", "vlan.fixedIpAssignments.e0:63:da:a9:f6:ec.name", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391053": {"network_name": "ON-Carp-100 Cardevco Rd", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391054": {"network_name": "ON-Chatham-80 Barthe Street", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391055": {"network_name": "ON-StThomas-614 Talbot St", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391139": {"network_name": "ON-Owen<PERSON>ound-104-<PERSON> room4A", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391140": {"network_name": "ON-StoneyCreek-486 <PERSON><PERSON>win <PERSON>", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391142": {"network_name": "ON-Newmarket-122 BalesDr", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977391143": {"network_name": "ON-Etobocike-12 Lockport Ave", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391145": {"network_name": "ON-Kitchener-599 Wabanaki Dr", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391147": {"network_name": "ON-Tillsonburg-60 Cedar St", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391148": {"network_name": "ON-Lively-129 Fielding Rd", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391149": {"network_name": "ON-NiagaraFalls-7017 Oakwood Dr", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391150": {"network_name": "ON-Gormley-23 <PERSON><PERSON> Dr", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977391151": {"network_name": "ON-Toronto-2 Queen St East", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977396625": {"network_name": "ON-Windsor-2679 Howard <PERSON>", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977396626": {"network_name": "ON-Kitchener- 221 <PERSON> Dr", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977396666": {"network_name": "QC-VaudreuilDorion-305 Bd Cite Des Jeunes", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977396763": {"network_name": "ON-Simcoe-40 Park RD", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977396775": {"network_name": "ON-Durham-15 Hardwood AveS", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977397468": {"network_name": " ON-DC2-LondonGRV-970GreenValley", "vlan_count": 2, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977397626": {"network_name": "ON-Kingston-626 Cataraqui Woods Dr", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977397627": {"network_name": "ON-Nepean-11 Bentley Ave", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977398304": {"network_name": "ON-OwenSound-1020 3rdAveE", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977398306": {"network_name": "ON-Astorville-49 BelecqueRd", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977398307": {"network_name": "ON-Hanover-301205 Knapville Rd", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977400161": {"network_name": "SK-Regina-140 4thAveEast", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977400633": {"network_name": "QC-Repentigny-1500 Rue Raymond", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977400996": {"network_name": "AB-Fort McKay-42_Canterra road", "vlan_count": 8, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977401494": {"network_name": "QC-Boisbriand-4243Marcel-Lacasse", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977401554": {"network_name": "AB-Calgary-350 7 Ave SW #2400", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977403064": {"network_name": "QC-Beloeil-1400Louis-Marchand", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977403540": {"network_name": "ON-New Liskeard- 41 Golding St", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977403541": {"network_name": "MB-Winnipeg- 1103 Pacific Ave", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}, "L_580401401977403542": {"network_name": "ON-Bradford-140 Holland St W", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977404934": {"network_name": "QC-<PERSON><PERSON> - 332 Ch. <PERSON>-<PERSON>Xavier", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405669": {"network_name": "BC-Victoria - 4206 Commerce Circle", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405670": {"network_name": "BC-Kelowna - 1494 Hardy Street", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405671": {"network_name": "BC-Surrey - 17535 55B AVE SURREY", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dhcpOptions[0].code", "vlan.dhcpOptions[0].type", "vlan.dhcpOptions[0].value", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405725": {"network_name": "BC-Abbotsford - 3032 <PERSON>", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405726": {"network_name": "BC-Duncan - 5271 Boal Rd", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405766": {"network_name": "BC-Burnaby-3267 Norland Ave", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dhcpOptions[0].code", "vlan.dhcpOptions[0].type", "vlan.dhcpOptions[0].value", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405768": {"network_name": "BC-Vernon - 2400_43 St", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405770": {"network_name": "BC-Richmond - 9871 RIVER DR", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dhcpOptions[0].code", "vlan.dhcpOptions[0].type", "vlan.dhcpOptions[0].value", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405772": {"network_name": "BC-Maple Ridge -11469 KINGSTON ST", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dhcpOptions[0].code", "vlan.dhcpOptions[0].type", "vlan.dhcpOptions[0].value", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405774": {"network_name": "BC-Kamloops - 1460 Ord Rd", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405775": {"network_name": "BC-Vancouver - 1465 THORNTON ST", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dhcpOptions[0].code", "vlan.dhcpOptions[0].type", "vlan.dhcpOptions[0].value", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405776": {"network_name": "BC-Vancouver - 2625 SKEENA ST", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405777": {"network_name": "BC-South Kitimat - 780 Lahakas Blvd", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405778": {"network_name": "BC-CHILLIWACK - 44275 Yale Rd West", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977405780": {"network_name": "NT-Yellowknife - 107 Kam Lake Road", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dhcpOptions[0].code", "vlan.dhcpOptions[0].type", "vlan.dhcpOptions[0].value", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977407357": {"network_name": "QC-Chateauguay - 2825 Bl. Ford", "vlan_count": 5, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "L_580401401977407732": {"network_name": "ON-London-4183 Blakie Rd", "vlan_count": 4, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.subnet"]}, "N_3850014731448353124": {"network_name": "ON-LondonSOV2-535 Sovereign", "vlan_count": 6, "sample_vlan_fields": ["vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}}, "clients": {"L_3850014731448352773": {"network_name": "QC-Quebec - 2680 boul. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448352782": {"network_name": "ON-Brampton-7324 Kennedy Rd South", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448352833": {"network_name": "AB-Calgary - 1119 46 Ave SE", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448352836": {"network_name": "QC-Chambly-940 Av. simard", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448352838": {"network_name": "ON-Etobicoke - 21 Steinway Blvd", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448352973": {"network_name": "AB-Airdrie - 90 Highland Park", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448352978": {"network_name": "ON-Welland - 1644 Merrittville", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448353014": {"network_name": "QC-Brossard-1040 Rue du Lux-suite 510", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448353020": {"network_name": "QC-Boucherville-701 des Ateliers", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448353071": {"network_name": "SK-Regina - 900 9th Ave", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.is11beCapable", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_3850014731448353194": {"network_name": " BC-DC3-Vancouver-1050 <PERSON><PERSON>", "client_count": 2, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390565": {"network_name": " QC-DC1_Boucherville-1350Nobel-<PERSON><PERSON><PERSON>", "client_count": 1, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390574": {"network_name": "QC-EastAngus-65<PERSON>illard", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390934": {"network_name": "QC-<PERSON><PERSON>rook-1075Talbot", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390938": {"network_name": "QC-Sherbrook-60KingO", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390941": {"network_name": "QC-Beloeil-1500Louis-Marchand", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390942": {"network_name": "QC-Chateauguay-210BlvdIndustriel", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390943": {"network_name": "QC-Boucherville-220J.-A.<PERSON>ardier", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390972": {"network_name": "QC-Montreal-1717<PERSON><PERSON><PERSON>", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977390975": {"network_name": "QC-St-Jean-Richelieu-720 Trotter", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391043": {"network_name": "ON-LondonSOV1-430 Sovereign", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391045": {"network_name": "ON-London-573 Admiral", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391046": {"network_name": "ON-Woodstock-1-925 Devonshire", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391048": {"network_name": "ON-Mississauga-110 <PERSON><PERSON>", "client_count": 7, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391053": {"network_name": "ON-Carp-100 Cardevco Rd", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391054": {"network_name": "ON-Chatham-80 Barthe Street", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391055": {"network_name": "ON-StThomas-614 Talbot St", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391139": {"network_name": "ON-Owen<PERSON>ound-104-<PERSON> room4A", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391140": {"network_name": "ON-StoneyCreek-486 <PERSON><PERSON>win <PERSON>", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391143": {"network_name": "ON-Etobocike-12 Lockport Ave", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391145": {"network_name": "ON-Kitchener-599 Wabanaki Dr", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391147": {"network_name": "ON-Tillsonburg-60 Cedar St", "client_count": 5, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391148": {"network_name": "ON-Lively-129 Fielding Rd", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391149": {"network_name": "ON-NiagaraFalls-7017 Oakwood Dr", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391150": {"network_name": "ON-Gormley-23 <PERSON><PERSON> Dr", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977391151": {"network_name": "ON-Toronto-2 Queen St East", "client_count": 9, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977396625": {"network_name": "ON-Windsor-2679 Howard <PERSON>", "client_count": 4, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977396626": {"network_name": "ON-Kitchener- 221 <PERSON> Dr", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977396666": {"network_name": "QC-VaudreuilDorion-305 Bd Cite Des Jeunes", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977396763": {"network_name": "ON-Simcoe-40 Park RD", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977396775": {"network_name": "ON-Durham-15 Hardwood AveS", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977397468": {"network_name": " ON-DC2-LondonGRV-970GreenValley", "client_count": 1, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977397626": {"network_name": "ON-Kingston-626 Cataraqui Woods Dr", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977397627": {"network_name": "ON-Nepean-11 Bentley Ave", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977398304": {"network_name": "ON-OwenSound-1020 3rdAveE", "client_count": 6, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977398306": {"network_name": "ON-Astorville-49 BelecqueRd", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977398307": {"network_name": "ON-Hanover-301205 Knapville Rd", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977400633": {"network_name": "QC-Repentigny-1500 Rue Raymond", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977400996": {"network_name": "AB-Fort McKay-42_Canterra road", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977401494": {"network_name": "QC-Boisbriand-4243Marcel-Lacasse", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977401554": {"network_name": "AB-Calgary-350 7 Ave SW #2400", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977403064": {"network_name": "QC-Beloeil-1400Louis-Marchand", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977403540": {"network_name": "ON-New Liskeard- 41 Golding St", "client_count": 6, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977403541": {"network_name": "MB-Winnipeg- 1103 Pacific Ave", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977403542": {"network_name": "ON-Bradford-140 Holland St W", "client_count": 3, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977404934": {"network_name": "QC-<PERSON><PERSON> - 332 Ch. <PERSON>-<PERSON>Xavier", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405670": {"network_name": "BC-Kelowna - 1494 Hardy Street", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405671": {"network_name": "BC-Surrey - 17535 55B AVE SURREY", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405725": {"network_name": "BC-Abbotsford - 3032 <PERSON>", "client_count": 5, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405726": {"network_name": "BC-Duncan - 5271 Boal Rd", "client_count": 3, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405766": {"network_name": "BC-Burnaby-3267 Norland Ave", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405768": {"network_name": "BC-Vernon - 2400_43 St", "client_count": 1, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405770": {"network_name": "BC-Richmond - 9871 RIVER DR", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405772": {"network_name": "BC-Maple Ridge -11469 KINGSTON ST", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405775": {"network_name": "BC-Vancouver - 1465 THORNTON ST", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405778": {"network_name": "BC-CHILLIWACK - 44275 Yale Rd West", "client_count": 1, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977405780": {"network_name": "NT-Yellowknife - 107 Kam Lake Road", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977407357": {"network_name": "QC-Chateauguay - 2825 Bl. Ford", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "L_580401401977407732": {"network_name": "ON-London-4183 Blakie Rd", "client_count": 4, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}, "N_3850014731448353124": {"network_name": "ON-LondonSOV2-535 Sovereign", "client_count": 10, "sample_client_fields": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.lastSeen", "client.mac", "client.manufacturer", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities"]}}, "ssids": {"L_3850014731448353071": {"network_name": "SK-Regina - 900 9th Ave", "ssid_count": 15, "sample_ssid_fields": ["ssid.authMode", "ssid.availabilityTags", "ssid.availableOnAllAps", "ssid.bandSelection", "ssid.defaultVlanId", "ssid.dot11r", "ssid.dot11r.adaptive", "ssid.dot11r.enabled", "ssid.dot11w", "ssid.dot11w.enabled", "ssid.dot11w.required", "ssid.enabled", "ssid.encryptionMode", "ssid.ipAssignmentMode", "ssid.lanIsolationEnabled", "ssid.mandatoryDhcpEnabled", "ssid.minBitrate", "ssid.name", "ssid.number", "ssid.perClientBandwidthLimitDown", "ssid.perClientBandwidthLimitUp", "ssid.perSsidBandwidthLimitDown", "ssid.perSsidBandwidthLimitUp", "ssid.psk", "ssid.radiusAccountingEnabled", "ssid.radiusAccountingInterimInterval", "ssid.radiusAccountingServers", "ssid.radiusAccountingServers[0].caCertificate", "ssid.radiusAccountingServers[0].host", "ssid.radiusAccountingServers[0].id", "ssid.radiusAccountingServers[0].openRoamingCertificateId", "ssid.radiusAccountingServers[0].port", "ssid.radiusAccountingServers[0].radsecEnabled", "ssid.radiusAttributeForGroupPolicies", "ssid.radiusAuthenticationNasId", "ssid.radiusCalledStationId", "ssid.radiusCoaEnabled", "ssid.radiusFallbackEnabled", "ssid.radiusOverride", "ssid.radiusProxyEnabled", "ssid.radiusServerAttemptsLimit", "ssid.radiusServerTimeout", "ssid.radiusServers", "ssid.radiusServers[0].caCertificate", "ssid.radiusServers[0].host", "ssid.radiusServers[0].id", "ssid.radiusServers[0].openRoamingCertificateId", "ssid.radiusServers[0].port", "ssid.radiusServers[0].radsecEnabled", "ssid.radiusTestingEnabled", "ssid.speedBurst", "ssid.speedBurst.enabled", "ssid.splashPage", "ssid.ssidAdminAccessible", "ssid.useVlanTagging", "ssid.visible", "ssid.wpaEncryptionMode"]}, "L_580401401977400161": {"network_name": "SK-Regina-140 4thAveEast", "ssid_count": 15, "sample_ssid_fields": ["ssid.authMode", "ssid.availabilityTags", "ssid.availableOnAllAps", "ssid.bandSelection", "ssid.defaultVlanId", "ssid.dot11r", "ssid.dot11r.adaptive", "ssid.dot11r.enabled", "ssid.dot11w", "ssid.dot11w.enabled", "ssid.dot11w.required", "ssid.enabled", "ssid.encryptionMode", "ssid.ipAssignmentMode", "ssid.lanIsolationEnabled", "ssid.mandatoryDhcpEnabled", "ssid.minBitrate", "ssid.name", "ssid.number", "ssid.perClientBandwidthLimitDown", "ssid.perClientBandwidthLimitUp", "ssid.perSsidBandwidthLimitDown", "ssid.perSsidBandwidthLimitUp", "ssid.psk", "ssid.radiusAccountingEnabled", "ssid.radiusAccountingInterimInterval", "ssid.radiusAccountingServers", "ssid.radiusAccountingServers[0].caCertificate", "ssid.radiusAccountingServers[0].host", "ssid.radiusAccountingServers[0].id", "ssid.radiusAccountingServers[0].openRoamingCertificateId", "ssid.radiusAccountingServers[0].port", "ssid.radiusAccountingServers[0].radsecEnabled", "ssid.radiusAttributeForGroupPolicies", "ssid.radiusAuthenticationNasId", "ssid.radiusCalledStationId", "ssid.radiusCoaEnabled", "ssid.radiusFallbackEnabled", "ssid.radiusOverride", "ssid.radiusProxyEnabled", "ssid.radiusServerAttemptsLimit", "ssid.radiusServerTimeout", "ssid.radiusServers", "ssid.radiusServers[0].caCertificate", "ssid.radiusServers[0].host", "ssid.radiusServers[0].id", "ssid.radiusServers[0].openRoamingCertificateId", "ssid.radiusServers[0].port", "ssid.radiusServers[0].radsecEnabled", "ssid.radiusTestingEnabled", "ssid.speedBurst", "ssid.speedBurst.enabled", "ssid.splashPage", "ssid.ssidAdminAccessible", "ssid.useVlanTagging", "ssid.visible", "ssid.wpaEncryptionMode"]}}, "all_field_paths": ["client.adaptivePolicyGroup", "client.description", "client.deviceTypePrediction", "client.firstSeen", "client.groupPolicy8021x", "client.id", "client.ip", "client.ip6", "client.ip6Local", "client.is11beCapable", "client.lastSeen", "client.mac", "client.manufacturer", "client.named<PERSON><PERSON>", "client.notes", "client.os", "client.pskGroup", "client.recentDeviceConnection", "client.recentDeviceMac", "client.recentDeviceName", "client.recentDeviceSerial", "client.smInstalled", "client.ssid", "client.status", "client.switchport", "client.usage", "client.usage.recv", "client.usage.sent", "client.usage.total", "client.user", "client.vlan", "client.wirelessCapabilities", "device.address", "device.details", "device.firmware", "device.floorPlanId", "device.lanIp", "device.lat", "device.lng", "device.mac", "device.model", "device.name", "device.networkId", "device.notes", "device.serial", "device.switchProfileId", "device.tags", "device.url", "device.wan1Ip", "device.wan2Ip", "network.enrollmentString", "network.id", "network.isBoundToConfigTemplate", "network.isVirtual", "network.name", "network.notes", "network.organizationId", "network.productTypes", "network.tags", "network.timeZone", "network.url", "org.api", "org.api.enabled", "org.cloud", "org.cloud.region", "org.cloud.region.host", "org.cloud.region.host.name", "org.cloud.region.name", "org.id", "org.licensing", "org.licensing.model", "org.management", "org.management.details", "org.management.details[0].name", "org.management.details[0].value", "org.name", "org.samlConsumerUrl", "org.samlConsumerUrls", "org.url", "ssid.authMode", "ssid.availabilityTags", "ssid.availableOnAllAps", "ssid.bandSelection", "ssid.defaultVlanId", "ssid.dot11r", "ssid.dot11r.adaptive", "ssid.dot11r.enabled", "ssid.dot11w", "ssid.dot11w.enabled", "ssid.dot11w.required", "ssid.enabled", "ssid.encryptionMode", "ssid.ipAssignmentMode", "ssid.lanIsolationEnabled", "ssid.mandatoryDhcpEnabled", "ssid.minBitrate", "ssid.name", "ssid.number", "ssid.perClientBandwidthLimitDown", "ssid.perClientBandwidthLimitUp", "ssid.perSsidBandwidthLimitDown", "ssid.perSsidBandwidthLimitUp", "ssid.psk", "ssid.radiusAccountingEnabled", "ssid.radiusAccountingInterimInterval", "ssid.radiusAccountingServers", "ssid.radiusAccountingServers[0].caCertificate", "ssid.radiusAccountingServers[0].host", "ssid.radiusAccountingServers[0].id", "ssid.radiusAccountingServers[0].openRoamingCertificateId", "ssid.radiusAccountingServers[0].port", "ssid.radiusAccountingServers[0].radsecEnabled", "ssid.radiusAttributeForGroupPolicies", "ssid.radiusAuthenticationNasId", "ssid.radiusCalledStationId", "ssid.radiusCoaEnabled", "ssid.radiusFallbackEnabled", "ssid.radiusOverride", "ssid.radiusProxyEnabled", "ssid.radiusServerAttemptsLimit", "ssid.radiusServerTimeout", "ssid.radiusServers", "ssid.radiusServers[0].caCertificate", "ssid.radiusServers[0].host", "ssid.radiusServers[0].id", "ssid.radiusServers[0].openRoamingCertificateId", "ssid.radiusServers[0].port", "ssid.radiusServers[0].radsecEnabled", "ssid.radiusTestingEnabled", "ssid.speedBurst", "ssid.speedBurst.enabled", "ssid.splashPage", "ssid.ssidAdminAccessible", "ssid.useVlanTagging", "ssid.visible", "ssid.wpaEncryptionMode", "vlan.applianceIp", "vlan.dhcpBootOptionsEnabled", "vlan.dhcpHandling", "vlan.dhcpLeaseTime", "vlan.dhcpOptions", "vlan.dhcpOptions[0].code", "vlan.dhcpOptions[0].type", "vlan.dhcpOptions[0].value", "vlan.dnsNameservers", "vlan.fixedIpAssignments", "vlan.fixedIpAssignments.18:e8:29:08:66:05", "vlan.fixedIpAssignments.18:e8:29:08:66:05.ip", "vlan.fixedIpAssignments.18:e8:29:08:66:05.name", "vlan.fixedIpAssignments.18:e8:29:08:66:28", "vlan.fixedIpAssignments.18:e8:29:08:66:28.ip", "vlan.fixedIpAssignments.18:e8:29:08:66:28.name", "vlan.fixedIpAssignments.e0:63:da:a9:ed:da", "vlan.fixedIpAssignments.e0:63:da:a9:ed:da.ip", "vlan.fixedIpAssignments.e0:63:da:a9:ed:da.name", "vlan.fixedIpAssignments.e0:63:da:a9:ef:da", "vlan.fixedIpAssignments.e0:63:da:a9:ef:da.ip", "vlan.fixedIpAssignments.e0:63:da:a9:ef:da.name", "vlan.fixedIpAssignments.e0:63:da:a9:f1:fc", "vlan.fixedIpAssignments.e0:63:da:a9:f1:fc.ip", "vlan.fixedIpAssignments.e0:63:da:a9:f1:fc.name", "vlan.fixedIpAssignments.e0:63:da:a9:f4:e1", "vlan.fixedIpAssignments.e0:63:da:a9:f4:e1.ip", "vlan.fixedIpAssignments.e0:63:da:a9:f4:e1.name", "vlan.fixedIpAssignments.e0:63:da:a9:f6:ec", "vlan.fixedIpAssignments.e0:63:da:a9:f6:ec.ip", "vlan.fixedIpAssignments.e0:63:da:a9:f6:ec.name", "vlan.groupPolicyId", "vlan.id", "vlan.interfaceId", "vlan.ipv6", "vlan.ipv6.enabled", "vlan.mandatoryDhcp", "vlan.mandatoryDhcp.enabled", "vlan.name", "vlan.networkId", "vlan.reservedIpRanges", "vlan.reservedIpRanges[0].comment", "vlan.reservedIpRanges[0].end", "vlan.reservedIpRanges[0].start", "vlan.subnet"]}