MERAKI COMPREHENSIVE DATA FIELD CATALOG
========================================

Based on your existing comprehensive Meraki data, here are ALL available fields
organized by category. Use this to decide exactly what you want to collect for all sites.

ORGANIZATION LEVEL FIELDS:
--------------------------
• org.id - Organization ID
• org.name - Organization name (e.g., "MPaaS-Transdev Canada Inc")
• org.url - Organization dashboard URL
• org.samlConsumerUrl - SAML consumer URL
• org.samlConsumerUrls[] - Array of SAML URLs
• org.api.enabled - API enabled status
• org.licensing.model - Licensing model (e.g., "per-device")
• org.cloud.region.name - Cloud region (e.g., "North America")
• org.cloud.region.host.name - Cloud host (e.g., "United States")
• org.management.details[].name - Management detail names
• org.management.details[].value - Management detail values (e.g., customer number)

NETWORK/SITE LEVEL FIELDS:
---------------------------
• network.id - Network ID
• network.organizationId - Parent organization ID
• network.name - Site name (e.g., "QC-Quebec - 2680 boul. Wilfrid-Hamel")
• network.productTypes[] - Product types (appliance, switch, wireless, etc.)
• network.timeZone - Time zone (e.g., "America/Montreal")
• network.tags[] - Network tags
• network.enrollmentString - Enrollment string
• network.url - Network dashboard URL
• network.notes - Network notes/description
• network.isBoundToConfigTemplate - Template binding status
• network.isVirtual - Virtual network status

DEVICE LEVEL FIELDS:
--------------------
• device.lat - Latitude coordinate
• device.lng - Longitude coordinate
• device.address - Physical address
• device.serial - Device serial number
• device.mac - MAC address
• device.lanIp - LAN IP address
• device.wan1Ip - Primary WAN IP address
• device.wan2Ip - Secondary WAN IP address
• device.notes - Device notes (contains ISP info, contacts, ICCID)
• device.tags[] - Device tags (Day2, SP numbers, SS numbers)
• device.url - Device dashboard URL
• device.networkId - Parent network ID
• device.name - Device name (e.g., "TDCAN-QCQUEB-FW01")
• device.details[] - Additional device details
• device.model - Device model (MX67C-NA, MS120-24P, MG21-NA, etc.)
• device.switchProfileId - Switch profile ID
• device.firmware - Firmware version
• device.floorPlanId - Floor plan ID

VLAN CONFIGURATION FIELDS:
---------------------------
• vlan.id - VLAN ID number
• vlan.networkId - Parent network ID
• vlan.name - VLAN name (VOIP, DATA, Wifi ME, Wifi Guest, etc.)
• vlan.applianceIp - Appliance IP address
• vlan.subnet - Subnet CIDR (e.g., "*************/26")
• vlan.groupPolicyId - Group policy ID
• vlan.fixedIpAssignments{} - Fixed IP assignments by MAC
• vlan.fixedIpAssignments.{mac}.ip - Assigned IP address
• vlan.fixedIpAssignments.{mac}.name - Device name for assignment
• vlan.reservedIpRanges[] - Reserved IP ranges
• vlan.reservedIpRanges[].start - Range start IP
• vlan.reservedIpRanges[].end - Range end IP
• vlan.reservedIpRanges[].comment - Range comment
• vlan.dnsNameservers - DNS nameservers (newline separated)
• vlan.dhcpHandling - DHCP handling method
• vlan.dhcpLeaseTime - DHCP lease time
• vlan.dhcpBootOptionsEnabled - DHCP boot options status
• vlan.dhcpOptions[] - DHCP options array
• vlan.dhcpOptions[].type - Option type
• vlan.dhcpOptions[].code - Option code
• vlan.dhcpOptions[].value - Option value
• vlan.interfaceId - Interface ID
• vlan.ipv6.enabled - IPv6 enabled status
• vlan.mandatoryDhcp.enabled - Mandatory DHCP status

CLIENT/DEVICE TRACKING FIELDS:
-------------------------------
• client.id - Client ID
• client.mac - Client MAC address
• client.description - Client description/hostname
• client.ip - Current IP address
• client.ip6 - IPv6 address
• client.ip6Local - Link-local IPv6 address
• client.user - Associated user
• client.firstSeen - First seen timestamp
• client.lastSeen - Last seen timestamp
• client.manufacturer - Device manufacturer
• client.os - Operating system
• client.deviceTypePrediction - Predicted device type
• client.recentDeviceSerial - Connected device serial
• client.recentDeviceName - Connected device name
• client.recentDeviceMac - Connected device MAC
• client.recentDeviceConnection - Connection type (Wired/Wireless)
• client.ssid - Connected SSID (if wireless)
• client.vlan - VLAN assignment
• client.switchport - Switch port number
• client.usage.sent - Bytes sent
• client.usage.recv - Bytes received
• client.usage.total - Total bytes
• client.status - Online/Offline status
• client.notes - Client notes
• client.groupPolicy8021x - 802.1X group policy
• client.adaptivePolicyGroup - Adaptive policy group
• client.smInstalled - Systems Manager installed
• client.namedVlan - Named VLAN
• client.pskGroup - PSK group
• client.wirelessCapabilities - Wireless capabilities

WIRELESS SSID FIELDS:
---------------------
• ssid.number - SSID number (0-14)
• ssid.name - SSID name
• ssid.enabled - Enabled status
• ssid.splashPage - Splash page type
• ssid.ssidAdminAccessible - Admin accessible
• ssid.authMode - Authentication mode
• ssid.encryptionMode - Encryption mode
• ssid.wpaEncryptionMode - WPA encryption mode
• ssid.radiusServers[] - RADIUS servers
• ssid.radiusAccountingServers[] - RADIUS accounting servers
• ssid.radiusAccountingEnabled - RADIUS accounting enabled
• ssid.radiusEnabled - RADIUS enabled
• ssid.radiusAttributeForGroupPolicies - RADIUS attribute for group policies
• ssid.radiusFailoverPolicy - RADIUS failover policy
• ssid.radiusLoadBalancingPolicy - RADIUS load balancing policy
• ssid.ipAssignmentMode - IP assignment mode
• ssid.useVlanTagging - VLAN tagging enabled
• ssid.defaultVlanId - Default VLAN ID
• ssid.availableOnAllAps - Available on all APs
• ssid.availabilityTags[] - Availability tags
• ssid.perClientBandwidthLimitUp - Upload bandwidth limit
• ssid.perClientBandwidthLimitDown - Download bandwidth limit
• ssid.visible - SSID visibility
• ssid.minBitrate - Minimum bitrate
• ssid.bandSelection - Band selection
• ssid.perSsidBandwidthLimitUp - Per-SSID upload limit
• ssid.perSsidBandwidthLimitDown - Per-SSID download limit

CONTACT INFORMATION (from device notes):
----------------------------------------
• ISP Information (Videotron, Bell, Telus)
• ICCID numbers for cellular connections
• Contact names and phone numbers
• Email addresses
• Service provider numbers (SP/SS codes)

WAN CONFIGURATION INDICATORS:
-----------------------------
• wan1Ip presence indicates primary WAN configuration
• wan2Ip presence indicates secondary/backup WAN
• Notes contain ISP details and connection types (PPPoE, etc.)
• Device models indicate connection types (MX = firewall, MG = cellular)

RECOMMENDED FIELDS FOR SITE INVENTORY:
======================================

BASIC SITE INFO:
• network.name (Site Name)
• device.address (Site Address)
• network.notes (Site Notes)
• network.tags[] (Site Tags)

DEVICE INFO:
• device.name (Device Name)
• device.model (Device Model)
• device.serial (Serial Number)
• device.wan1Ip (Primary WAN IP)
• device.wan2Ip (Secondary WAN IP)
• device.notes (ISP/Contact Info)
• device.tags[] (Device Tags)

NETWORK CONFIG:
• vlan.name + vlan.subnet (VLAN Configuration)
• client count (Active Devices)
• ssid.name (Wireless Networks)

WAN STATUS DETECTION:
• wan1Ip/wan2Ip presence = Static IP
• Notes containing "PPPoE" = Dynamic/DHCP
• Notes containing ISP names = Provider info

NEXT STEPS:
===========
Review this catalog and tell me exactly which fields you want to collect.
I can then create a script to extract those specific fields for all 67 sites.

Example request: "I want site name, address, WAN IPs, device models, 
contact info from notes, and VLAN subnets for all sites."
