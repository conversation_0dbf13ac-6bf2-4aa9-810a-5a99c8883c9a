COMPREHENSIVE MERAKI DATA FIELD CATALOG
============================================================

This catalog shows ALL available data fields from your Meraki environment.
Use this to decide exactly what fields you want to collect for all sites.

ORGANIZATION FIELDS:
-------------------
  org.api
  org.api.enabled
  org.cloud
  org.cloud.region
  org.cloud.region.host
  org.cloud.region.host.name
  org.cloud.region.name
  org.id
  org.licensing
  org.licensing.model
  org.management
  org.management.details
  org.management.details[0].name
  org.management.details[0].value
  org.name
  org.samlConsumerUrl
  org.samlConsumerUrls
  org.url

NETWORK FIELDS:
--------------
  network.enrollmentString
  network.id
  network.isBoundToConfigTemplate
  network.isVirtual
  network.name
  network.notes
  network.organizationId
  network.productTypes
  network.tags
  network.timeZone
  network.url

DEVICE FIELDS:
-------------
  device.address
  device.details
  device.firmware
  device.floorPlanId
  device.lanIp
  device.lat
  device.lng
  device.mac
  device.model
  device.name
  device.networkId
  device.notes
  device.serial
  device.switchProfileId
  device.tags
  device.url
  device.wan1Ip
  device.wan2Ip

VLAN FIELDS:
-----------
  vlan.applianceIp
  vlan.dhcpBootOptionsEnabled
  vlan.dhcpHandling
  vlan.dhcpLeaseTime
  vlan.dhcpOptions
  vlan.dhcpOptions[0].code
  vlan.dhcpOptions[0].type
  vlan.dhcpOptions[0].value
  vlan.dnsNameservers
  vlan.fixedIpAssignments
  vlan.fixedIpAssignments.18:e8:29:08:66:05
  vlan.fixedIpAssignments.18:e8:29:08:66:05.ip
  vlan.fixedIpAssignments.18:e8:29:08:66:05.name
  vlan.fixedIpAssignments.18:e8:29:08:66:28
  vlan.fixedIpAssignments.18:e8:29:08:66:28.ip
  vlan.fixedIpAssignments.18:e8:29:08:66:28.name
  vlan.fixedIpAssignments.e0:63:da:a9:ed:da
  vlan.fixedIpAssignments.e0:63:da:a9:ed:da.ip
  vlan.fixedIpAssignments.e0:63:da:a9:ed:da.name
  vlan.fixedIpAssignments.e0:63:da:a9:ef:da
  vlan.fixedIpAssignments.e0:63:da:a9:ef:da.ip
  vlan.fixedIpAssignments.e0:63:da:a9:ef:da.name
  vlan.fixedIpAssignments.e0:63:da:a9:f1:fc
  vlan.fixedIpAssignments.e0:63:da:a9:f1:fc.ip
  vlan.fixedIpAssignments.e0:63:da:a9:f1:fc.name
  vlan.fixedIpAssignments.e0:63:da:a9:f4:e1
  vlan.fixedIpAssignments.e0:63:da:a9:f4:e1.ip
  vlan.fixedIpAssignments.e0:63:da:a9:f4:e1.name
  vlan.fixedIpAssignments.e0:63:da:a9:f6:ec
  vlan.fixedIpAssignments.e0:63:da:a9:f6:ec.ip
  vlan.fixedIpAssignments.e0:63:da:a9:f6:ec.name
  vlan.groupPolicyId
  vlan.id
  vlan.interfaceId
  vlan.ipv6
  vlan.ipv6.enabled
  vlan.mandatoryDhcp
  vlan.mandatoryDhcp.enabled
  vlan.name
  vlan.networkId
  vlan.reservedIpRanges
  vlan.reservedIpRanges[0].comment
  vlan.reservedIpRanges[0].end
  vlan.reservedIpRanges[0].start
  vlan.subnet

CLIENT FIELDS:
-------------
  client.adaptivePolicyGroup
  client.description
  client.deviceTypePrediction
  client.firstSeen
  client.groupPolicy8021x
  client.id
  client.ip
  client.ip6
  client.ip6Local
  client.is11beCapable
  client.lastSeen
  client.mac
  client.manufacturer
  client.namedVlan
  client.notes
  client.os
  client.pskGroup
  client.recentDeviceConnection
  client.recentDeviceMac
  client.recentDeviceName
  client.recentDeviceSerial
  client.smInstalled
  client.ssid
  client.status
  client.switchport
  client.usage
  client.usage.recv
  client.usage.sent
  client.usage.total
  client.user
  client.vlan
  client.wirelessCapabilities

SSID FIELDS:
-----------
  ssid.authMode
  ssid.availabilityTags
  ssid.availableOnAllAps
  ssid.bandSelection
  ssid.defaultVlanId
  ssid.dot11r
  ssid.dot11r.adaptive
  ssid.dot11r.enabled
  ssid.dot11w
  ssid.dot11w.enabled
  ssid.dot11w.required
  ssid.enabled
  ssid.encryptionMode
  ssid.ipAssignmentMode
  ssid.lanIsolationEnabled
  ssid.mandatoryDhcpEnabled
  ssid.minBitrate
  ssid.name
  ssid.number
  ssid.perClientBandwidthLimitDown
  ssid.perClientBandwidthLimitUp
  ssid.perSsidBandwidthLimitDown
  ssid.perSsidBandwidthLimitUp
  ssid.psk
  ssid.radiusAccountingEnabled
  ssid.radiusAccountingInterimInterval
  ssid.radiusAccountingServers
  ssid.radiusAccountingServers[0].caCertificate
  ssid.radiusAccountingServers[0].host
  ssid.radiusAccountingServers[0].id
  ssid.radiusAccountingServers[0].openRoamingCertificateId
  ssid.radiusAccountingServers[0].port
  ssid.radiusAccountingServers[0].radsecEnabled
  ssid.radiusAttributeForGroupPolicies
  ssid.radiusAuthenticationNasId
  ssid.radiusCalledStationId
  ssid.radiusCoaEnabled
  ssid.radiusFallbackEnabled
  ssid.radiusOverride
  ssid.radiusProxyEnabled
  ssid.radiusServerAttemptsLimit
  ssid.radiusServerTimeout
  ssid.radiusServers
  ssid.radiusServers[0].caCertificate
  ssid.radiusServers[0].host
  ssid.radiusServers[0].id
  ssid.radiusServers[0].openRoamingCertificateId
  ssid.radiusServers[0].port
  ssid.radiusServers[0].radsecEnabled
  ssid.radiusTestingEnabled
  ssid.speedBurst
  ssid.speedBurst.enabled
  ssid.splashPage
  ssid.ssidAdminAccessible
  ssid.useVlanTagging
  ssid.visible
  ssid.wpaEncryptionMode

SUMMARY STATISTICS:
--------------------
Total unique field paths: 181
Organizations analyzed: 1
Networks with devices: 79
Networks with VLANs: 79
Networks with clients: 70
Networks with SSIDs: 2

SAMPLE DATA EXAMPLES:
--------------------
Here are some example values for key fields:

Organization Name: MPaaS-Transdev Canada Inc
Sample Network: QC-Quebec - 2680 boul. Wilfrid-Hamel
Device Count: 2
VLAN Count: 5

RECOMMENDED FIELDS FOR SITE INVENTORY:
----------------------------------------
Based on your existing scripts, you might want:
  • network.name (Site Name)
  • device.address (Site Address)
  • device.notes (Contact info, ISP details)
  • device.tags (Site tags)
  • device.model (Device model)
  • device.serial (Serial number)
  • device.wan1Ip (WAN IP address)
  • device.wan2Ip (Secondary WAN IP)
  • vlan.subnet (VLAN subnets)
  • vlan.name (VLAN names)

Let me know which specific fields you want to collect!