{"organization": {"id": "580401401977375879", "name": "MPaaS-Transdev Canada Inc", "url": "https://n839.meraki.com/o/8EqHfaF/manage/organization/overview", "samlConsumerUrl": "https://n839.meraki.com/saml/login/8EqHfaF/KuolMbCvqlMc", "samlConsumerUrls": ["https://n839.meraki.com/saml/login/8EqHfaF/KuolMbCvqlMc", "https://n839.meraki.com/saml/login/8EqHfaF/GcwiQdCvqlMc", "https://n839.meraki.com/saml/login/8EqHfaF/yq27jdCvqlMc"], "api": {"enabled": true}, "licensing": {"model": "per-device"}, "cloud": {"region": {"name": "North America", "host": {"name": "United States"}}}, "management": {"details": [{"name": "customer number", "value": "95235567"}]}}, "target_network": {"id": "L_3850014731448352773", "organizationId": "580401401977375879", "name": "QC-Quebec - 2680 boul. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "productTypes": ["appliance", "switch"], "timeZone": "America/Montreal", "tags": [], "enrollmentString": null, "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/pwznDapBb/manage/clients", "notes": "", "isBoundToConfigTemplate": false, "isVirtual": false}, "devices": [{"lat": 46.80687, "lng": -71.3007, "address": "2680-B boul. <PERSON><PERSON><PERSON><PERSON><PERSON> , Québec , Qc, H1P 2J1", "serial": "Q4AE-XU69-ENGC", "mac": "cc:9c:3e:8d:59:73", "lanIp": "***********", "notes": "Videotron : 78013944-001-7 \nTelus.com.inc\nICCID: 8912230102144952417\n\n<PERSON><PERSON>and\nTél: ************\nemail: <PERSON><PERSON><PERSON>@transdev.com\n#2 sebastien boud<PERSON><PERSON>\nTél:************\nemail : <EMAIL>", "tags": ["Day2", "SP550516", "SS277297"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/9TMjrapBb/manage/nodes/new_list/224971436415347", "networkId": "L_3850014731448352773", "name": "TDCAN-QCQUEB-SW01", "details": [], "model": "MS120-24P", "switchProfileId": null, "firmware": "switch-17-1-3", "floorPlanId": null}, {"lat": 46.8069, "lng": -71.30071, "address": "2680 boul. <PERSON><PERSON><PERSON><PERSON> , Québec , Qc, H1P 2J1", "serial": "Q2HY-23TL-M4HB", "mac": "bc:db:09:bc:d3:f4", "wan1Ip": "***********", "wan2Ip": null, "notes": "Videotron : 78013944-001-7 \nTelus.com.inc\nICCID: 8912230102144952417\n\n<PERSON><PERSON>and\nTél: ************\nemail: <PERSON><PERSON><PERSON>@transdev.com\n#2 sebastien boud<PERSON><PERSON>\nTél:************\nemail : <EMAIL>", "tags": ["Day2", "SP550521", "SS277297"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/pwznDapBb/manage/nodes/new_list/207648947229684", "networkId": "L_3850014731448352773", "name": "TDCAN-QCQUEB-FW01", "details": [], "model": "MX67C-NA", "firmware": "wired-19-1-7", "floorPlanId": null}], "vlans": [{"id": 10, "networkId": "L_3850014731448352773", "name": "VOIP", "applianceIp": "*************", "subnet": "*************/26", "groupPolicyId": "100", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "*******\n1.0.0.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "3850014731448352803", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 20, "networkId": "L_3850014731448352773", "name": "DATA", "applianceIp": "***********", "subnet": "***********/26", "groupPolicyId": "102", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "**********\n10.40.64.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "3850014731448352804", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 30, "networkId": "L_3850014731448352773", "name": "Wifi ME", "applianceIp": "************", "subnet": "************/26", "groupPolicyId": "102", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "**********\n10.40.64.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "3850014731448352805", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 40, "networkId": "L_3850014731448352773", "name": "Wifi Guest", "applianceIp": "**********", "subnet": "**********/26", "groupPolicyId": "100", "fixedIpAssignments": {}, "reservedIpRanges": [], "dnsNameservers": "*******\n1.0.0.3", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [], "interfaceId": "3850014731448352806", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}, {"id": 90, "networkId": "L_3850014731448352773", "name": "DEV_MGMT", "applianceIp": "***********", "subnet": "***********/26", "fixedIpAssignments": {"ac:8b:a9:8c:33:d0": {"ip": "***********", "name": null}}, "reservedIpRanges": [], "dnsNameservers": "**********\n10.40.64.4", "dhcpHandling": "Run a DHCP server", "dhcpLeaseTime": "4 hours", "dhcpBootOptionsEnabled": false, "dhcpOptions": [{"type": "text", "code": "15", "value": "limocar.int"}], "interfaceId": "3850014731448352807", "ipv6": {"enabled": false}, "mandatoryDhcp": {"enabled": false}}], "clients": [{"id": "k6d1b3d", "mac": "a6:fc:4d:ba:7e:a3", "description": "Galaxy-A53-5G", "ip": "**********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-04-26T14:59:50Z", "lastSeen": "2025-05-28T17:03:48Z", "manufacturer": null, "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "40", "switchport": "5", "usage": {"sent": 395920, "recv": 1243503, "total": 1639423}, "status": "Online", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "k734633", "mac": "6c:3c:7c:bd:8d:38", "description": "CanonBD8D38", "ip": "***********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-04-26T11:14:20Z", "lastSeen": "2025-05-28T17:02:21Z", "manufacturer": "Canon", "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "20", "switchport": "1", "usage": {"sent": 593, "recv": 640, "total": 1234}, "status": "Online", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "k7a32ac", "mac": "3e:88:8c:0a:5a:e7", "description": "Galaxy-Tab-A7-Lite", "ip": "**********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-04-26T15:57:33Z", "lastSeen": "2025-05-28T17:03:48Z", "manufacturer": null, "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "40", "switchport": "5", "usage": {"sent": 4903, "recv": 16959, "total": 21863}, "status": "Online", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "k82ad8f", "mac": "ac:8b:a9:8c:33:d0", "description": "AP01", "ip": "***********", "ip6": null, "ip6Local": "fe80:0:0:0:ae8b:a9ff:fe8c:33d0", "user": null, "firstSeen": "2024-04-25T22:30:37Z", "lastSeen": "2025-05-28T17:03:48Z", "manufacturer": "Ubiquiti", "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "90", "switchport": "5", "usage": {"sent": 18530, "recv": 3449, "total": 21979}, "status": "Online", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "k8ad4d4", "mac": "da:19:3f:a6:14:a6", "description": "Galaxy-Tab-A7-Lite", "ip": "**********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-04-29T18:07:50Z", "lastSeen": "2025-05-28T12:40:37Z", "manufacturer": null, "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "40", "switchport": "5", "usage": {"sent": 2305, "recv": 16485, "total": 18790}, "status": "Offline", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "k8f47e7", "mac": "80:38:fb:d4:3f:14", "description": "LAPT001876", "ip": "***********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-05-24T12:38:54Z", "lastSeen": "2025-05-27T17:52:20Z", "manufacturer": "Intel", "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "20", "switchport": "5", "usage": {"sent": 323742, "recv": 329498, "total": 653240}, "status": "Offline", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "k9e0399", "mac": "52:06:ab:2f:6d:8b", "description": "Galaxy-Tab-A7-Lite", "ip": "**********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-04-27T10:17:52Z", "lastSeen": "2025-05-28T17:03:48Z", "manufacturer": null, "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "40", "switchport": "5", "usage": {"sent": 2194, "recv": 4467, "total": 6661}, "status": "Online", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "ka23b0a", "mac": "bc:03:58:cb:c1:c2", "description": "LAPT001459", "ip": "***************", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-05-01T10:24:47Z", "lastSeen": "2025-05-28T13:35:14Z", "manufacturer": "Intel", "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "20", "switchport": "5", "usage": {"sent": 2, "recv": 0, "total": 2}, "status": "Offline", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "kb3f149", "mac": "6a:44:78:68:e6:65", "description": "Galaxy-A13-5G", "ip": "**********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2025-05-27T12:15:35Z", "lastSeen": "2025-05-27T17:47:11Z", "manufacturer": null, "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "40", "switchport": "5", "usage": {"sent": 193, "recv": 347, "total": 541}, "status": "Offline", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}, {"id": "kb5b404", "mac": "ce:a7:d4:31:89:68", "description": "Galaxy-Tab-A7-Lite", "ip": "**********", "ip6": null, "ip6Local": null, "user": null, "firstSeen": "2024-04-26T15:17:36Z", "lastSeen": "2025-05-28T04:00:03Z", "manufacturer": null, "os": null, "deviceTypePrediction": null, "recentDeviceSerial": "Q4AE-XU69-ENGC", "recentDeviceName": "TDCAN-QCQUEB-SW01", "recentDeviceMac": "cc:9c:3e:8d:59:73", "recentDeviceConnection": "Wired", "ssid": null, "vlan": "40", "switchport": "5", "usage": {"sent": 1903, "recv": 4454, "total": 6357}, "status": "Offline", "notes": null, "groupPolicy8021x": null, "adaptivePolicyGroup": null, "smInstalled": false, "namedVlan": null, "pskGroup": null, "wirelessCapabilities": "802.11b - 2.4 GHz", "mcgSerial": null, "mcgNodeName": null, "mcgNodeMac": null, "mcgNetworkId": null}], "ssids": null, "network_details": {"id": "L_3850014731448352773", "organizationId": "580401401977375879", "productTypes": ["appliance", "switch"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/9TMjrapBb/manage/usage/list", "name": "QC-Quebec - 2680 boul. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeZone": "America/Montreal", "enrollmentString": null, "tags": [], "notes": "", "isBoundToConfigTemplate": false, "isVirtual": false}, "device_details": {"Q4AE-XU69-ENGC": {"lat": 46.80687, "lng": -71.3007, "address": "2680-B boul. <PERSON><PERSON><PERSON><PERSON><PERSON> , Québec , Qc, H1P 2J1", "serial": "Q4AE-XU69-ENGC", "mac": "cc:9c:3e:8d:59:73", "lanIp": "***********", "notes": "Videotron : 78013944-001-7 \nTelus.com.inc\nICCID: 8912230102144952417\n\n<PERSON><PERSON>and\nTél: ************\nemail: <PERSON><PERSON><PERSON>@transdev.com\n#2 sebastien boud<PERSON><PERSON>\nTél:************\nemail : <EMAIL>", "tags": ["Day2", "SP550516", "SS277297"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/9TMjrapBb/manage/nodes/new_list/224971436415347", "networkId": "L_3850014731448352773", "name": "TDCAN-QCQUEB-SW01", "details": [], "model": "MS120-24P", "switchProfileId": null, "firmware": "switch-17-1-3", "floorPlanId": null}, "Q2HY-23TL-M4HB": {"lat": 46.8069, "lng": -71.30071, "address": "2680 boul. <PERSON><PERSON><PERSON><PERSON> , Québec , Qc, H1P 2J1", "serial": "Q2HY-23TL-M4HB", "mac": "bc:db:09:bc:d3:f4", "wan1Ip": "***********", "wan2Ip": null, "notes": "Videotron : 78013944-001-7 \nTelus.com.inc\nICCID: 8912230102144952417\n\n<PERSON><PERSON>and\nTél: ************\nemail: <PERSON><PERSON><PERSON>@transdev.com\n#2 sebastien boud<PERSON><PERSON>\nTél:************\nemail : <EMAIL>", "tags": ["Day2", "SP550521", "SS277297"], "url": "https://n839.meraki.com/QC-Quebec-2680-b/n/pwznDapBb/manage/nodes/new_list/207648947229684", "networkId": "L_3850014731448352773", "name": "TDCAN-QCQUEB-FW01", "details": [], "model": "MX67C-NA", "firmware": "wired-19-1-7", "floorPlanId": null}}, "firewall_l3_rules": {"rules": [{"comment": "Default rule", "policy": "allow", "protocol": "Any", "srcPort": "Any", "srcCidr": "Any", "destPort": "Any", "destCidr": "Any", "syslogEnabled": false}]}, "site_to_site_vpn": {"mode": "spoke", "hubs": [{"hubId": "L_580401401977390565", "useDefaultRoute": false}], "subnets": [{"localSubnet": "*************/26", "useVpn": false}, {"localSubnet": "***********/26", "useVpn": true}, {"localSubnet": "************/26", "useVpn": true}, {"localSubnet": "**********/26", "useVpn": false}, {"localSubnet": "***********/26", "useVpn": true}], "subnet": {"nat": {"isAllowed": false}}}}