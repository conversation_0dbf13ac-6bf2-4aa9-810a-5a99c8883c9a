#!/usr/bin/env python3
"""
Debug script to see the actual structure of uplink settings API response
"""

import meraki
import json

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

def debug_uplink_settings():
    """Debug uplink settings for CHILLIWACK site"""
    print("Debugging uplink settings API response...")
    
    try:
        dashboard = meraki.DashboardAPI(
            api_key=MERAKI_API_KEY,
            output_log=False,
            print_console=False,
            suppress_logging=True
        )
        
        # Get organizations
        orgs = dashboard.organizations.getOrganizations()
        org_id = orgs[0]['id']
        
        # Get networks
        networks = dashboard.organizations.getOrganizationNetworks(org_id)
        
        # Find CHILLIWACK network
        chilliwack_network = None
        for network in networks:
            if "CHILLIWACK" in network['name'].upper():
                chilliwack_network = network
                break
        
        if not chilliwack_network:
            print("CHILLIWACK network not found")
            return
        
        print(f"Found: {chilliwack_network['name']}")
        
        # Get devices
        devices = dashboard.networks.getNetworkDevices(chilliwack_network['id'])
        
        # Find MX device
        mx_device = None
        for device in devices:
            if device.get('model', '').startswith('MX'):
                mx_device = device
                break
        
        if not mx_device:
            print("No MX device found")
            return
        
        print(f"Found MX device: {mx_device.get('name', mx_device['serial'])}")
        print(f"Model: {mx_device.get('model', 'Unknown')}")
        print(f"Serial: {mx_device.get('serial', 'Unknown')}")
        
        # Get uplink settings
        print("\nGetting uplink settings...")
        uplink_settings = dashboard.appliance.getDeviceApplianceUplinksSettings(mx_device['serial'])
        
        print("\nFull uplink settings response:")
        print(json.dumps(uplink_settings, indent=2))
        
        # Check device data for comparison
        print(f"\nDevice WAN IPs from device data:")
        print(f"  wan1Ip: {mx_device.get('wan1Ip', 'None')}")
        print(f"  wan2Ip: {mx_device.get('wan2Ip', 'None')}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_uplink_settings()
