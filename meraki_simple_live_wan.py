#!/usr/bin/env python3
"""
Script: meraki_simple_live_wan.py

Simple live WAN analysis using only confirmed working Meraki API methods.
Gets WAN configuration settings to determine Static vs DHCP setup.
"""

import meraki
from typing import Dict, List, Any, NamedTuple
from pathlib import Path
from datetime import datetime
import csv
import time

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

class WANInterface(NamedTuple):
    interface: str  # WAN1, WAN2
    ip_address: str
    connection_type: str  # Static, DHCP, Unknown
    enabled: str  # enabled, disabled
    
class SiteWANInfo(NamedTuple):
    site_name: str
    network_id: str
    wan_interfaces: List[WANInterface]
    has_cellular_gateway: bool
    cellular_model: str
    primary_device_model: str
    switch_models: List[str]
    switch_count: int
    notes: str

def setup_meraki() -> meraki.DashboardAPI:
    """Initialize Meraki dashboard API"""
    return meraki.DashboardAPI(
        api_key=MERAKI_API_KEY,
        output_log=False,
        print_console=False,
        suppress_logging=True
    )

def safe_api_call(func, *args, **kwargs):
    """Safely make API calls with error handling"""
    try:
        result = func(*args, **kwargs)
        time.sleep(0.1)  # Rate limiting
        return result
    except Exception as e:
        print(f"    API Error: {e}")
        return None

def analyze_device_wan_settings(dashboard: meraki.DashboardAPI, device: Dict[str, Any]) -> List[WANInterface]:
    """Analyze WAN settings for a single device"""
    wan_interfaces = []
    serial = device.get('serial', '')
    
    if not device.get('model', '').startswith(('MX', 'Z')):
        return wan_interfaces
    
    print(f"      Getting WAN settings for: {device.get('name', serial)}")
    
    # Get uplink settings
    uplink_settings = safe_api_call(dashboard.appliance.getDeviceApplianceUplinksSettings, serial)
    
    if not uplink_settings:
        # Fallback to stored device data
        wan1_ip = device.get('wan1Ip')
        wan2_ip = device.get('wan2Ip')
        
        if wan1_ip:
            wan_interfaces.append(WANInterface(
                interface="WAN1",
                ip_address=wan1_ip,
                connection_type="Static (from device data)",
                enabled="enabled"
            ))
        
        if wan2_ip:
            wan_interfaces.append(WANInterface(
                interface="WAN2", 
                ip_address=wan2_ip,
                connection_type="Static (from device data)",
                enabled="enabled"
            ))
        
        return wan_interfaces
    
    # Process WAN1 settings
    wan1_config = uplink_settings.get('wan1', {})
    if wan1_config:
        wan_enabled = wan1_config.get('wanEnabled', 'disabled')
        using_static_ip = wan1_config.get('usingStaticIp', False)
        
        if wan_enabled == 'enabled':
            if using_static_ip:
                static_ip = wan1_config.get('staticIp', 'Not configured')
                wan_interfaces.append(WANInterface(
                    interface="WAN1",
                    ip_address=static_ip,
                    connection_type="Static",
                    enabled=wan_enabled
                ))
            else:
                # DHCP configuration - IP not available in settings
                wan_interfaces.append(WANInterface(
                    interface="WAN1",
                    ip_address="DHCP (IP not available in settings)",
                    connection_type="DHCP",
                    enabled=wan_enabled
                ))
    
    # Process WAN2 settings
    wan2_config = uplink_settings.get('wan2', {})
    if wan2_config:
        wan_enabled = wan2_config.get('wanEnabled', 'disabled')
        using_static_ip = wan2_config.get('usingStaticIp', False)
        
        if wan_enabled == 'enabled':
            if using_static_ip:
                static_ip = wan2_config.get('staticIp', 'Not configured')
                wan_interfaces.append(WANInterface(
                    interface="WAN2",
                    ip_address=static_ip,
                    connection_type="Static",
                    enabled=wan_enabled
                ))
            else:
                # DHCP configuration - IP not available in settings
                wan_interfaces.append(WANInterface(
                    interface="WAN2",
                    ip_address="DHCP (IP not available in settings)",
                    connection_type="DHCP",
                    enabled=wan_enabled
                ))
    
    return wan_interfaces

def analyze_site_simple(dashboard: meraki.DashboardAPI, network: Dict[str, Any]) -> SiteWANInfo:
    """Analyze WAN configuration for a single site"""
    site_name = network['name']
    network_id = network['id']
    
    print(f"    Processing: {site_name}")
    
    # Get devices
    devices = safe_api_call(dashboard.networks.getNetworkDevices, network_id)
    
    if not devices:
        return SiteWANInfo(
            site_name=site_name,
            network_id=network_id,
            wan_interfaces=[],
            has_cellular_gateway=False,
            cellular_model="",
            primary_device_model="No devices found",
            switch_models=[],
            switch_count=0,
            notes=""
        )
    
    wan_interfaces = []
    has_cellular_gateway = False
    cellular_model = ""
    primary_device_model = ""
    switch_models = []
    combined_notes = ""
    
    # Find appliance and switch devices
    appliance_devices = [d for d in devices if d.get('model', '').startswith(('MX', 'Z', 'MG'))]
    switch_devices = [d for d in devices if d.get('model', '').startswith('MS')]
    
    # Extract switch information
    for switch in switch_devices:
        model = switch.get('model', '')
        if model and model not in switch_models:
            switch_models.append(model)
    
    # Process appliance devices
    for device in appliance_devices:
        model = device.get('model', '')
        notes = device.get('notes', '')
        combined_notes += f"{notes} " if notes else ""
        
        # Check for cellular gateway
        if model.startswith('MG'):
            has_cellular_gateway = True
            cellular_model = model
        
        # Set primary device model
        if model.startswith('MX') or not primary_device_model:
            primary_device_model = model
        
        # Get WAN settings for this device
        device_wan_interfaces = analyze_device_wan_settings(dashboard, device)
        wan_interfaces.extend(device_wan_interfaces)
    
    return SiteWANInfo(
        site_name=site_name,
        network_id=network_id,
        wan_interfaces=wan_interfaces,
        has_cellular_gateway=has_cellular_gateway,
        cellular_model=cellular_model,
        primary_device_model=primary_device_model,
        switch_models=switch_models,
        switch_count=len(switch_devices),
        notes=combined_notes.strip()
    )

def main():
    print("MERAKI SIMPLE LIVE WAN ANALYSIS")
    print("=" * 40)
    print("Getting WAN configuration settings for all sites...")
    print("This will show DHCP-configured interfaces even without current IP addresses.")
    print()
    
    dashboard = setup_meraki()
    
    print("Getting organizations...")
    orgs = safe_api_call(dashboard.organizations.getOrganizations)
    if not orgs:
        print("No organizations found")
        return
    
    org = orgs[0]
    print(f"Using organization: {org['name']}")
    
    print("Getting networks...")
    networks = safe_api_call(dashboard.organizations.getOrganizationNetworks, org['id'])
    if not networks:
        print("No networks found")
        return
    
    print(f"Found {len(networks)} networks. Analyzing...")
    
    all_sites = []
    
    for i, network in enumerate(networks, 1):
        print(f"  [{i}/{len(networks)}] {network['name']}")
        
        try:
            site_info = analyze_site_simple(dashboard, network)
            all_sites.append(site_info)
        except Exception as e:
            print(f"    Error: {e}")
            all_sites.append(SiteWANInfo(
                site_name=network['name'],
                network_id=network['id'],
                wan_interfaces=[],
                has_cellular_gateway=False,
                cellular_model="",
                primary_device_model=f"Error: {e}",
                switch_models=[],
                switch_count=0,
                notes=""
            ))
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_file = Path(f'meraki_simple_wan_config_{timestamp}.csv')
    
    with csv_file.open('w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        writer.writerow([
            'Site Name', 'Network ID', 'Primary Device', 
            'WAN1 IP', 'WAN1 Type', 'WAN1 Status',
            'WAN2 IP', 'WAN2 Type', 'WAN2 Status',
            'Has Cellular', 'Cellular Model',
            'Switch Count', 'Switch Models', 'Notes'
        ])
        
        for site in all_sites:
            wan1_ip = wan1_type = wan1_status = ""
            wan2_ip = wan2_type = wan2_status = ""
            
            for wan in site.wan_interfaces:
                if wan.interface == "WAN1":
                    wan1_ip = wan.ip_address
                    wan1_type = wan.connection_type
                    wan1_status = wan.enabled
                elif wan.interface == "WAN2":
                    wan2_ip = wan.ip_address
                    wan2_type = wan.connection_type
                    wan2_status = wan.enabled
            
            writer.writerow([
                site.site_name, site.network_id, site.primary_device_model,
                wan1_ip, wan1_type, wan1_status,
                wan2_ip, wan2_type, wan2_status,
                "YES" if site.has_cellular_gateway else "NO", site.cellular_model,
                site.switch_count, ', '.join(site.switch_models),
                site.notes.replace('\n', ' | ')[:200] if site.notes else ""
            ])
    
    print(f"\nAnalysis complete! Results saved to: {csv_file}")
    
    # Show DHCP sites
    dhcp_sites = [s for s in all_sites if any("DHCP" in w.connection_type for w in s.wan_interfaces)]
    if dhcp_sites:
        print(f"\nFound {len(dhcp_sites)} sites with DHCP-configured WAN interfaces:")
        for site in dhcp_sites:
            print(f"  - {site.site_name}")
            for wan in site.wan_interfaces:
                if "DHCP" in wan.connection_type:
                    print(f"    {wan.interface}: {wan.connection_type}")

if __name__ == "__main__":
    main()
