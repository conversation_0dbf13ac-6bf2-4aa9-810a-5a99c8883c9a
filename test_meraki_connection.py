#!/usr/bin/env python3
"""
Simple test script to verify Meraki API connection
"""

import meraki
import sys

# Meraki Configuration
MERAKI_API_KEY = "****************************************"

def test_connection():
    """Test basic Meraki API connection"""
    print("Testing Meraki API connection...")
    
    try:
        dashboard = meraki.DashboardAPI(
            api_key=MERAKI_API_KEY,
            output_log=False,
            print_console=False,
            suppress_logging=True
        )
        
        print("Getting organizations...")
        orgs = dashboard.organizations.getOrganizations()
        
        if orgs:
            print(f"Success! Found {len(orgs)} organizations:")
            for org in orgs:
                print(f"  - {org['name']} (ID: {org['id']})")
            
            # Get networks from first org
            org_id = orgs[0]['id']
            print(f"\nGetting networks for organization: {orgs[0]['name']}")
            
            networks = dashboard.organizations.getOrganizationNetworks(org_id)
            if networks:
                print(f"Found {len(networks)} networks:")
                for i, net in enumerate(networks[:5]):  # Show first 5
                    print(f"  {i+1}. {net['name']} (ID: {net['id']})")
                if len(networks) > 5:
                    print(f"  ... and {len(networks) - 5} more")
                
                # Test getting details for first network
                if networks:
                    first_net = networks[0]
                    print(f"\nTesting detailed data for first network: {first_net['name']}")
                    
                    # Try to get devices
                    try:
                        devices = dashboard.networks.getNetworkDevices(first_net['id'])
                        print(f"  Devices: {len(devices) if devices else 0}")
                    except Exception as e:
                        print(f"  Devices: Error - {e}")
                    
                    # Try to get VLANs
                    try:
                        vlans = dashboard.appliance.getNetworkApplianceVlans(first_net['id'])
                        print(f"  VLANs: {len(vlans) if vlans else 0}")
                    except Exception as e:
                        print(f"  VLANs: Error - {e}")
                    
                    print("\nAPI connection test completed successfully!")
                    return True
            else:
                print("No networks found")
        else:
            print("No organizations found")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
